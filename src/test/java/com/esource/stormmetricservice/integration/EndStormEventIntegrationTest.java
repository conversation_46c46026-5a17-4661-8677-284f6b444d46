package com.esource.stormmetricservice.integration;

import java.time.Duration;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.DirtiesContext.ClassMode;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import com.esource.AbstractIntegrationTestContainer;
import com.esource.stormmetricservice.etr.model.ActiveOutages;
import com.esource.stormmetricservice.etr.model.ETR;
import com.esource.stormmetricservice.etr.repository.ActiveOutagesRepository;
import com.esource.stormmetricservice.etr.repository.ETRRepository;
import com.esource.stormmetricservice.metrics.model.Metric;
import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.metrics.model.metrics.OutageMetric;
import com.esource.stormmetricservice.metrics.model.metrics.PredictionMetric;
import com.esource.stormmetricservice.metrics.model.metrics.ResourceMetric;
import com.esource.stormmetricservice.metrics.model.metrics.RestorationMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

@SpringBootTest
@ActiveProfiles(profiles = {"test", "disableSecurity"})
@TestPropertySource(
        properties = {
            "rabbitmq.queue.stormEnd=storm.storm-event.metric-service.storm-end.queue.test-event",
            "spring.data.mongodb.database=testdb2"
        })
@DirtiesContext(classMode = ClassMode.BEFORE_CLASS)
class EndStormEventIntegrationTest extends AbstractIntegrationTestContainer {

    @Autowired
    private MetricRepository metricRepository;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ActiveOutagesRepository outageRepository;

    @Autowired
    private ETRRepository etrRepository;

    @Autowired
    private ApplicationContext applicationContext;

    @BeforeEach
    void setup() {

        // wait until the queue is confirmed to be empty
        await().atMost(10, TimeUnit.SECONDS)
                .pollInterval(1, TimeUnit.SECONDS)
                .until(() ->
                        rabbitTemplate.receive("storm.storm-event.metric-service.storm-end.queue.test-event") == null);
        metricRepository.deleteAll();
        outageRepository.deleteAll();
    }

    @AfterEach
    void tearDown() {
        await().atMost(10, TimeUnit.SECONDS)
                .until(() ->
                        rabbitTemplate.receive("storm.storm-event.metric-service.storm-end.queue.test-event") == null);
        metricRepository.deleteAll();
        outageRepository.deleteAll();
    }

    @Test
    void testEndStormEventProcessing() {
        // Setup mock data
        EndStormEvent testEvent = createMockEndStormEvent("5f50c31e917a6a3a3e4cedf9");

        List<ActiveOutages> mockOutages = generateOutages();

        outageRepository.saveAll(mockOutages);

        List<ETR> mockETRs = generateMockETRs(testEvent.getStormData().getId());
        etrRepository.saveAll(mockETRs);

        StormMetric expectedPredictionMetric = createExpectedPredictionStormMetric(testEvent, mockETRs);

        StormMetric expectedResourceMetric = createExpectedResourceStormMetric(testEvent, mockETRs);

        StormMetric expectedOutageMetric = createExpectedOutageStormMetric(testEvent, mockOutages);

        StormMetric expectedRestorationMetric = createExpectedRestorationStormMetric(testEvent, mockOutages);

        // Send the mock event to the RabbitMQ queue
        rabbitTemplate.convertAndSend("storm.storm-event-exchange", "storm.storm-event.metric-service.test", testEvent);

        StormMetric actualMetric = await().atMost(Duration.ofSeconds(10))
                .until(
                        () -> metricRepository
                                .findByStormIdAndMetricType(
                                        new ObjectId(testEvent.getStormData().getId()), "outages")
                                .orElse(null),
                        Objects::nonNull);

        assertEquals(
                expectedOutageMetric.getMetrics().size(),
                actualMetric.getMetrics().size());

        assertEquals(expectedOutageMetric.getMetrics(), actualMetric.getMetrics());
        assertEquals(expectedOutageMetric.getStormId(), actualMetric.getStormId());
        assertEquals(expectedOutageMetric.getMetricType(), actualMetric.getMetricType());

        StormMetric actualPredictionMetric = await().atMost(Duration.ofSeconds(5000))
                .until(
                        () -> metricRepository
                                .findByStormIdAndMetricType(
                                        new ObjectId(testEvent.getStormData().getId()), "medium-predicted-restorations")
                                .orElse(null),
                        Objects::nonNull);

        assertEquals(
                expectedPredictionMetric.getMetrics().size(),
                actualPredictionMetric.getMetrics().size());
        assertEquals(expectedPredictionMetric.getMetrics(), actualPredictionMetric.getMetrics());
        assertEquals(expectedPredictionMetric.getStormId(), actualPredictionMetric.getStormId());
        assertEquals(expectedPredictionMetric.getMetricType(), actualPredictionMetric.getMetricType());

        StormMetric actualResourceMetric = await().atMost(Duration.ofSeconds(50))
                .until(
                        () -> metricRepository
                                .findByStormIdAndMetricType(
                                        new ObjectId(testEvent.getStormData().getId()), "resources")
                                .orElse(null),
                        Objects::nonNull);

        assertEquals(
                expectedResourceMetric.getMetrics().size(),
                actualResourceMetric.getMetrics().size());
        assertEquals(expectedResourceMetric.getMetrics(), actualResourceMetric.getMetrics());
        assertEquals(expectedResourceMetric.getStormId(), actualResourceMetric.getStormId());
        assertEquals(expectedResourceMetric.getMetricType(), actualResourceMetric.getMetricType());

        StormMetric actualRestorationMetric = await().atMost(Duration.ofSeconds(10))
                .until(
                        () -> metricRepository
                                .findByStormIdAndMetricType(
                                        new ObjectId(testEvent.getStormData().getId()), "restorations")
                                .orElse(null),
                        Objects::nonNull);

        assertEquals(
                expectedRestorationMetric.getMetrics().size(),
                actualRestorationMetric.getMetrics().size());
        assertEquals(expectedRestorationMetric.getMetrics(), actualRestorationMetric.getMetrics());
        assertEquals(expectedRestorationMetric.getStormId(), actualRestorationMetric.getStormId());
        assertEquals(expectedRestorationMetric.getMetricType(), actualRestorationMetric.getMetricType());
    }

    @Order(1)
    @Test
    void testEndStormEventProcessing_Idempotency() {
        // Setup mock data
        EndStormEvent testEvent = createMockEndStormEvent("5f50c31e917a6a3a3e4cedf7");

        List<ActiveOutages> mockOutages = generateOutages();
        outageRepository.saveAll(mockOutages);

        StormMetric existingMetric = createExpectedOutageStormMetric(testEvent, mockOutages);
        // Send the mock event to the RabbitMQ queue, create metric for storm
        rabbitTemplate.convertAndSend("storm.storm-event-exchange", "storm.storm-event.metric-service.test", testEvent);
        StormMetric originalMetric = await().atMost(Duration.ofSeconds(5))
                .until(
                        () -> metricRepository
                                .findByStormIdAndMetricType(
                                        new ObjectId(testEvent.getStormData().getId()), "outages")
                                .orElse(null),
                        Objects::nonNull);

        assertEquals(
                existingMetric.getMetrics().size(), originalMetric.getMetrics().size()); // should be 2

        // add outages
        List<ActiveOutages> modifiedOutages = new ArrayList<>(mockOutages);
        modifiedOutages.add(ActiveOutages.builder()
                .id("3")
                .lastUpdated(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.HOURS))
                .createdTimestamp(
                        ZonedDateTime.parse("2022-01-01T05:00:00.000Z").truncatedTo(ChronoUnit.HOURS))
                .generatedBy("system")
                .outageRecords(Arrays.asList(ActiveOutages.OutageRecord.builder()
                        .region("region3")
                        .activeCustomerOutages(3)
                        .activeIncidents(3)
                        .restoredCustomerOutages(3)
                        .restoredIncidents(3)
                        .build()))
                .build());

        outageRepository.saveAll(modifiedOutages);

        // Use the modified list for further operations
        StormMetric newMetricSameStorm = createExpectedOutageStormMetric(testEvent, modifiedOutages);

        // Send another event to the RabbitMQ queue
        rabbitTemplate.convertAndSend("storm.storm-event-exchange", "storm.storm-event.metric-service.test", testEvent);

        // Assert that the new metric does not overwrite the old one
        await().atMost(Duration.ofSeconds(10)).untilAsserted(() -> {
            StormMetric actualMetric = metricRepository
                    .findByStormIdAndMetricType(
                            new ObjectId(testEvent.getStormData().getId()), "outages")
                    .orElse(StormMetric.builder().build());

            assertNotEquals(actualMetric, newMetricSameStorm);
            assertEquals(2, actualMetric.getMetrics().size()); // should still be 2!

            assertEquals(originalMetric, actualMetric);
        });
    }

    private EndStormEvent createMockEndStormEvent(String stormId) {
        StormOverview stormData = StormOverview.builder()
                .id(stormId)
                .name("Test Storm")
                .startDate(ZonedDateTime.parse("2022-01-01T00:00:00Z"))
                .endDate(ZonedDateTime.parse("2022-01-02T00:00:00Z"))
                .build();

        return EndStormEvent.builder()
                .timestamp(ZonedDateTime.now(ZoneOffset.UTC))
                .eventName("stormEndEvent")
                .stormData(stormData)
                .build();
    }

    private StormMetric createExpectedOutageStormMetric(EndStormEvent event, List<ActiveOutages> outages) {
        List<Metric> metrics = outages.stream()
                .flatMap(outage -> outage.getOutageRecords().stream())
                .map(record -> OutageMetric.builder()
                        .records(List.of(OutageMetric.OutageRecord.builder()
                                .region(record.getRegion())
                                .activeCustomerOutages(record.getActiveCustomerOutages())
                                .activeIncidents(record.getActiveIncidents())
                                .build()))
                        .timestamp(ZonedDateTime.parse("2022-01-01T00:00:00Z"))
                        .build())
                .collect(Collectors.toList());

        return StormMetric.builder()
                .stormId(new ObjectId(event.getStormData().getId()))
                .metricType("outages")
                .stormData(event.getStormData())
                .metrics(metrics)
                .lastUpdated(ZonedDateTime.now(ZoneOffset.UTC))
                .build();
    }

    private StormMetric createExpectedRestorationStormMetric(EndStormEvent event, List<ActiveOutages> outages) {
        List<Metric> metrics = outages.stream()
                .flatMap(outage -> outage.getOutageRecords().stream())
                .map(record -> RestorationMetric.builder()
                        .records(List.of(RestorationMetric.RestorationRecord.builder()
                                .region(record.getRegion())
                                .restoredCustomerOutages(record.getRestoredCustomerOutages())
                                .restoredIncidents(record.getRestoredIncidents())
                                .build()))
                        .timestamp(ZonedDateTime.parse("2022-01-01T00:00:00Z"))
                        .build())
                .collect(Collectors.toList());

        return StormMetric.builder()
                .stormId(new ObjectId(event.getStormData().getId()))
                .metricType("restorations")
                .stormData(event.getStormData())
                .metrics(metrics)
                .lastUpdated(ZonedDateTime.now(ZoneOffset.UTC))
                .build();
    }

    private List<ActiveOutages> generateOutages() {

        ActiveOutages outage1 = ActiveOutages.builder()
                .id("1")
                .lastUpdated(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.HOURS))
                .createdTimestamp(
                        ZonedDateTime.parse("2022-01-01T00:00:00.000Z").truncatedTo(ChronoUnit.HOURS))
                .generatedBy("system")
                .outageRecords(Arrays.asList(ActiveOutages.OutageRecord.builder()
                        .region("region1")
                        .activeCustomerOutages(1)
                        .activeIncidents(1)
                        .restoredCustomerOutages(1)
                        .restoredIncidents(1)
                        .build()))
                .build();

        ActiveOutages outage2 = ActiveOutages.builder()
                .id("2")
                .lastUpdated(ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.HOURS))
                .createdTimestamp(
                        ZonedDateTime.parse("2022-01-02T00:00:00.000Z").truncatedTo(ChronoUnit.HOURS))
                .generatedBy("system")
                .outageRecords(Arrays.asList(ActiveOutages.OutageRecord.builder()
                        .region("region2")
                        .activeCustomerOutages(2)
                        .activeIncidents(2)
                        .restoredCustomerOutages(2)
                        .restoredIncidents(2)
                        .build()))
                .build();

        return Arrays.asList(outage1, outage2);
    }

    private List<ETR> generateMockETRs(String stormId) {
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
        String stormObjectId = stormId;

        ETR mockEtr = ETR.builder()
                .stormId(stormObjectId)
                .sessionId(new ObjectId())
                .generationTime(now.minusHours(1))
                .etrType("result")
                .stormStartDate(now.minusDays(1))
                .systemResources(Map.of("workerA", 2, "workerB", 4))
                .regionalETRs(Arrays.asList(
                        ETR.RegionalETR.builder()
                                .location("Edison")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(5))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build(),
                        ETR.RegionalETR.builder()
                                .location("McKeesport")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(6))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build()))
                .build();

        ETR mockEtr2 = ETR.builder()
                .stormId(stormObjectId)
                .sessionId(new ObjectId())
                .generationTime(now.minusHours(4))
                .etrType("result")
                .stormStartDate(now.minusDays(1))
                .systemResources(Map.of("workerA", 6, "workerB", 6))
                .regionalETRs(Arrays.asList(
                        ETR.RegionalETR.builder()
                                .location("Edison")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(8))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build(),
                        ETR.RegionalETR.builder()
                                .location("McKeesport")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(9))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build(),
                        ETR.RegionalETR.builder()
                                .location("New Location")
                                .outageScale("large")
                                .projectedETR(now.plusHours(11))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build()))
                .build();

        return Arrays.asList(mockEtr, mockEtr2);
    }

    private StormMetric createExpectedPredictionStormMetric(EndStormEvent event, List<ETR> mockETRs) {
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);

        List<PredictionMetric.PredictionRecord> recordsForFirstEtr = Arrays.asList(
                PredictionMetric.PredictionRecord.builder()
                        .region("Edison")
                        .hoursToRestoration(6.0)
                        .type("current")
                        .build(),
                PredictionMetric.PredictionRecord.builder()
                        .region("McKeesport")
                        .hoursToRestoration(7.0)
                        .type("current")
                        .build(),
                PredictionMetric.PredictionRecord.builder()
                        .region("System")
                        .hoursToRestoration(7.0)
                        .type("result")
                        .build());

        List<PredictionMetric.PredictionRecord> recordsForSecondEtr = Arrays.asList(
                PredictionMetric.PredictionRecord.builder()
                        .region("Edison")
                        .hoursToRestoration(12.0)
                        .type("current")
                        .build(),
                PredictionMetric.PredictionRecord.builder()
                        .region("McKeesport")
                        .hoursToRestoration(13.0)
                        .type("current")
                        .build(),
                PredictionMetric.PredictionRecord.builder()
                        .region("System")
                        .hoursToRestoration(13.0)
                        .type("result")
                        .build());

        List<Metric> metrics = Arrays.asList(
                PredictionMetric.builder()
                        .sessionId(mockETRs.get(0).getSessionId().toHexString())
                        .timestamp(now.minusHours(1))
                        .records(recordsForFirstEtr)
                        .build(),
                PredictionMetric.builder()
                        .sessionId(mockETRs.get(1).getSessionId().toHexString())
                        .timestamp(now.minusHours(4))
                        .records(recordsForSecondEtr)
                        .build());

        return StormMetric.builder()
                .stormId(new ObjectId(event.getStormData().getId()))
                .metricType("medium-predicted-restorations")
                .stormData(event.getStormData())
                .metrics(metrics)
                .lastUpdated(now)
                .build();
    }

    private StormMetric createExpectedResourceStormMetric(EndStormEvent event, List<ETR> mockETRs) {
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);

        List<ResourceMetric.ResourceRecord> recordsForFirstEtr = Arrays.asList(ResourceMetric.ResourceRecord.builder()
                .region("System")
                .totalWorkers(6.0)
                .resources(mockETRs.get(0).getSystemResources())
                .build());

        List<ResourceMetric.ResourceRecord> recordsForSecondEtr = Arrays.asList(ResourceMetric.ResourceRecord.builder()
                .region("System")
                .totalWorkers(12.0)
                .resources(mockETRs.get(1).getSystemResources())
                .build());

        List<Metric> metrics = Arrays.asList(
                ResourceMetric.builder()
                        .sessionId(mockETRs.get(0).getSessionId().toHexString())
                        .timestamp(now.minusHours(1))
                        .records(recordsForFirstEtr)
                        .build(),
                ResourceMetric.builder()
                        .sessionId(mockETRs.get(1).getSessionId().toHexString())
                        .timestamp(now.minusHours(4))
                        .records(recordsForSecondEtr)
                        .build());

        return StormMetric.builder()
                .stormId(new ObjectId(event.getStormData().getId()))
                .metricType("resources")
                .stormData(event.getStormData())
                .metrics(metrics)
                .lastUpdated(now)
                .build();
    }
}
