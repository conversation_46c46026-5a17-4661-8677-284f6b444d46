package com.esource.stormmetricservice.integration;

import java.time.Duration;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import com.esource.AbstractIntegrationTestContainer;
import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric;
import com.esource.stormmetricservice.aggregatemetrics.model.Overview;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Resource;
import com.esource.stormmetricservice.aggregatemetrics.repository.OverviewRepository;
import com.esource.stormmetricservice.etr.model.ActiveOutages;
import com.esource.stormmetricservice.etr.model.ResourceEntity;
import com.esource.stormmetricservice.etr.model.StormRecord;
import com.esource.stormmetricservice.etr.repository.ActiveOutagesRepository;
import com.esource.stormmetricservice.etr.repository.ResourceRepository;
import com.esource.stormmetricservice.etr.repository.StormRecordRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEvent;
import com.esource.stormmetricservice.weather.model.RawWeatherData;
import com.esource.stormmetricservice.weather.repository.WeatherRepository;
import com.fasterxml.jackson.databind.ObjectMapper;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@ActiveProfiles(profiles = {"test", "disableSecurity"})
@TestPropertySource(
        properties = {
            "metrics.aggregator.cumulative-outage.strategy=delta",
            "rabbitmq.queue.aggregateOverview=storm.storm-event.metric-service.aggregate-overview.queue.aggregate"
        })
class AggregateOverviewIntegrationTest extends AbstractIntegrationTestContainer {

    @Autowired
    private OverviewRepository overviewRepository;

    @Autowired
    private ActiveOutagesRepository activeOutagesRepository;

    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private StormRecordRepository stormRecordRepository;

    @Autowired
    private WeatherRepository weatherRepository;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String ACTIVE_OUTAGE_ID1 = "5f50c31e917a6a3a3e4cedf5";
    private static final String ACTIVE_OUTAGE_ID2 = "5f50c31e917a6a3a3e4cedf6";

    private static final ZoneId UTC = ZoneId.of("UTC");

    @BeforeEach
    void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(context)
                .defaultRequest(MockMvcRequestBuilders.get("/").locale(Locale.US))
                .build();

        await().atMost(10, TimeUnit.SECONDS)
                .pollInterval(1, TimeUnit.SECONDS)
                .until(() ->
                        rabbitTemplate.receive("storm.storm-event.metric-service.aggregate-overview.queue.aggregate")
                                == null);
    }

    @AfterEach
    void tearDown() {
        await().atMost(10, TimeUnit.SECONDS)
                .until(() ->
                        rabbitTemplate.receive("storm.storm-event.metric-service.aggregate-overview.queue.aggregate")
                                == null);
        overviewRepository.deleteAll();
        activeOutagesRepository.deleteAll();
        resourceRepository.deleteAll();
        weatherRepository.deleteAll();
    }

    @Test
    void testAggregateOverviewEvent() throws Exception {

        ZonedDateTime timestamp =
                ZonedDateTime.now().truncatedTo(ChronoUnit.MINUTES).withZoneSameInstant(UTC);

        ZonedDateTime previousTimestamp = timestamp.minusHours(1);

        List<ActiveOutages> activeOutages = createMockOutages(timestamp);
        activeOutagesRepository.saveAll(activeOutages);

        ResourceEntity resource = createMockResource();
        resourceRepository.save(resource);

        StormRecord stormRecord = createMockStormRecord();
        stormRecordRepository.save(stormRecord);

        List<RawWeatherData> rawWeatherData = createMultipleRawWeatherData(previousTimestamp, timestamp);
        weatherRepository.saveAll(rawWeatherData);

        Overview previousOverview = Overview.builder()
                .timestamp(previousTimestamp)
                .data(Overview.OverviewData.builder()
                        .outages(Map.of(
                                "Region1",
                                        Outage.builder()
                                                .totalIncidents(10)
                                                .totalAffectedCustomers(100)
                                                .uniqueIncidentsCount(10)
                                                .uniqueAffectedCustomers(100)
                                                .build(),
                                "Region2",
                                        Outage.builder()
                                                .totalIncidents(5)
                                                .totalAffectedCustomers(50)
                                                .uniqueIncidentsCount(5)
                                                .uniqueAffectedCustomers(50)
                                                .build(),
                                "System",
                                        Outage.builder()
                                                .totalIncidents(15)
                                                .totalAffectedCustomers(150)
                                                .uniqueIncidentsCount(15)
                                                .uniqueAffectedCustomers(150)
                                                .build()))
                        .resources(Map.of(
                                "System",
                                        Resource.builder()
                                                .totalResources(100)
                                                .resources(Map.of("contractWorkers", 60, "foreignWorkers", 40))
                                                .build(),
                                "Region1",
                                        Resource.builder()
                                                .totalResources(70)
                                                .resources(Map.of("contractWorkers", 40, "foreignWorkers", 30))
                                                .build(),
                                "Region2",
                                        Resource.builder()
                                                .totalResources(30)
                                                .resources(Map.of("contractWorkers", 20, "foreignWorkers", 10))
                                                .build()))
                        .build())
                .build();

        overviewRepository.save(previousOverview);

        // verify each of the repositories have data in them
        assertEquals(1, overviewRepository.count());
        assertEquals(2, activeOutagesRepository.count());
        assertEquals(1, resourceRepository.count());
        assertEquals(1, stormRecordRepository.count());
        assertEquals(2, weatherRepository.count());

        AggregateOverviewEvent event2 = AggregateOverviewEvent.builder()
                .timestamp(timestamp)
                .metadata(Map.of("activeOutageId", ACTIVE_OUTAGE_ID2))
                .build();

        rabbitTemplate.convertAndSend(
                "storm.storm-event-exchange", "storm.storm-aggregate-event.metric-service", event2);

        // This should come from the rawWeatherData, but I keep getting nulls
        Map<String, RawWeatherData.WeatherStation> mostRecentWeatherStations = new HashMap<>();
        mostRecentWeatherStations.put(
                "Station1",
                RawWeatherData.WeatherStation.builder()
                        .stationId(UUID.randomUUID())
                        .latitude(33.6598)
                        .longitude(-85.8314)
                        .region("Region1")
                        .weatherAttributes(RawWeatherData.WeatherAttributes.builder()
                                .temperatureCelsius(27.0)
                                .humidityPercent(65.0)
                                .windBearingDeg(190)
                                .windGustMph(35.0)
                                .windSpeedMph(20.0)
                                .build())
                        .build());

        mostRecentWeatherStations.put(
                "Station2",
                RawWeatherData.WeatherStation.builder()
                        .stationId(UUID.randomUUID())
                        .latitude(33.5207)
                        .longitude(-86.8025)
                        .region("Region1")
                        .weatherAttributes(RawWeatherData.WeatherAttributes.builder()
                                .temperatureCelsius(15.0)
                                .humidityPercent(15.0)
                                .windBearingDeg(15)
                                .windGustMph(15.0)
                                .windSpeedMph(50.0)
                                .build())
                        .build());

        mostRecentWeatherStations.put(
                "Station3",
                RawWeatherData.WeatherStation.builder()
                        .stationId(UUID.randomUUID())
                        .latitude(34.5207)
                        .longitude(-81.8025)
                        .region("Region2")
                        .weatherAttributes(RawWeatherData.WeatherAttributes.builder()
                                .temperatureCelsius(30.0)
                                .humidityPercent(50.0)
                                .windBearingDeg(230)
                                .windGustMph(50.0)
                                .windSpeedMph(5.0)
                                .build())
                        .build());

        Overview currentExpectedOverview = Overview.builder()
                .timestamp(timestamp)
                .data(Overview.OverviewData.builder()
                        .outages(new LinkedHashMap<>(Map.ofEntries(
                                Map.entry(
                                        "Region1",
                                        Outage.builder()
                                                .totalIncidents(12)
                                                .totalAffectedCustomers(120)
                                                .totalRawIncidents(10)
                                                .totalRawAffectedCustomers(100)
                                                .uniqueIncidentsCount(2)
                                                .uniqueAffectedCustomers(20)
                                                .build()),
                                Map.entry(
                                        "Region2",
                                        Outage.builder()
                                                .totalIncidents(2)
                                                .totalAffectedCustomers(20)
                                                .totalRawIncidents(10)
                                                .totalRawAffectedCustomers(100)
                                                .uniqueIncidentsCount(0)
                                                .uniqueAffectedCustomers(0)
                                                .build()),
                                Map.entry(
                                        "System",
                                        Outage.builder()
                                                .totalIncidents(14)
                                                .totalAffectedCustomers(140)
                                                .totalRawIncidents(20)
                                                .totalRawAffectedCustomers(200)
                                                .uniqueIncidentsCount(2)
                                                .uniqueAffectedCustomers(20)
                                                .build()))))
                        .resources(new LinkedHashMap<>(Map.ofEntries(
                                Map.entry(
                                        "Region1",
                                        Resource.builder()
                                                .totalResources(70)
                                                .resources(Map.of("contractWorkers", 40, "foreignWorkers", 30))
                                                .build()),
                                Map.entry(
                                        "Region2",
                                        Resource.builder()
                                                .totalResources(30)
                                                .resources(Map.of("contractWorkers", 20, "foreignWorkers", 10))
                                                .build()),
                                Map.entry(
                                        "System",
                                        Resource.builder()
                                                .totalResources(100)
                                                .resources(Map.of("contractWorkers", 60, "foreignWorkers", 40))
                                                .build()))))
                        .stationData(Arrays.asList(
                                mostRecentWeatherStations.get("Station1"),
                                mostRecentWeatherStations.get("Station2"),
                                mostRecentWeatherStations.get("Station3")))
                        .build())
                .build();

        List<Overview> actualOverviews = await().atMost(Duration.ofSeconds(1000))
                .until(
                        () -> overviewRepository.findByTimestampBetweenWithWeather(
                                previousTimestamp,
                                timestamp.plusMinutes(1),
                                previousTimestamp.truncatedTo(ChronoUnit.HOURS),
                                timestamp.plusMinutes(1).truncatedTo(ChronoUnit.HOURS)),
                        overviews -> overviews.size() == 2);

        Overview sortedActualOverview = sortOverview(actualOverviews.get(1));
        Overview sortedExpectedOverview = sortOverview(currentExpectedOverview);

        assertEquals(sortedExpectedOverview, sortedActualOverview);

        // P2: Aggregate calculations

        // set up parameters
        ZonedDateTime expectedStart = previousTimestamp.minusMinutes(1).withZoneSameInstant(ZoneOffset.UTC);
        ZonedDateTime expectedEnd = timestamp.plusMinutes(1).withZoneSameInstant(ZoneOffset.UTC);

        // set up json
        AggregateMetric expectedMetric = AggregateMetric.builder()
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(previousTimestamp)
                                        .totalIncidents(10)
                                        .totalAffectedCustomers(100)
                                        .totalRawIncidents(null)
                                        .totalRawAffectedCustomers(null)
                                        .uniqueIncidentsCount(10)
                                        .uniqueAffectedCustomers(100)
                                        .totalResources(70)
                                        .resources(Map.of("contractWorkers", 40, "foreignWorkers", 30))
                                        .averageWindSpeed(20.0)
                                        .windGustMph(30.0)
                                        .averageWindSpeed(12.5)
                                        .windGustThresholds(Map.of(
                                                "over20mph", 1,
                                                "over30mph", 0,
                                                "over40mph", 0))
                                        .totalRestorations(0)
                                        .totalAffectedCustomerRestorations(0)
                                        .cumulativeAffectedCustomers(100)
                                        .cumulativeRestorations(0)
                                        .cumulativeIncidents(10)
                                        .cumulativeCustomersRestored(0)
                                        .restoredPerWorker(0.0)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(timestamp)
                                        .totalIncidents(12)
                                        .totalAffectedCustomers(120)
                                        .totalRawIncidents(10)
                                        .totalRawAffectedCustomers(100)
                                        .uniqueIncidentsCount(2)
                                        .uniqueAffectedCustomers(20)
                                        .totalResources(70)
                                        .resources(Map.of("contractWorkers", 40, "foreignWorkers", 30))
                                        .windGustMph(32.0)
                                        .averageWindSpeed(35.0)
                                        .windGustThresholds(Map.of(
                                                "over20mph", 2,
                                                "over30mph", 1,
                                                "over40mph", 0))
                                        .totalRestorations(0)
                                        .totalAffectedCustomerRestorations(0)
                                        .cumulativeAffectedCustomers(120)
                                        .cumulativeRestorations(0)
                                        .cumulativeIncidents(12)
                                        .cumulativeCustomersRestored(0)
                                        .restoredPerWorker(0.0)
                                        .build()),
                        "Region2",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(previousTimestamp)
                                        .totalIncidents(5)
                                        .totalAffectedCustomers(50)
                                        .totalRawIncidents(null)
                                        .totalRawAffectedCustomers(null)
                                        .uniqueIncidentsCount(5)
                                        .uniqueAffectedCustomers(50)
                                        .totalResources(30)
                                        .resources(Map.of("contractWorkers", 20, "foreignWorkers", 10))
                                        .windGustMph(40.0)
                                        .averageWindSpeed(2.0)
                                        .windGustThresholds(Map.of(
                                                "over20mph", 1,
                                                "over30mph", 1,
                                                "over40mph", 0))
                                        .totalRestorations(0)
                                        .totalAffectedCustomerRestorations(0)
                                        .cumulativeAffectedCustomers(50)
                                        .cumulativeRestorations(0)
                                        .cumulativeIncidents(5)
                                        .cumulativeCustomersRestored(0)
                                        .restoredPerWorker(0.0)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(timestamp)
                                        .totalIncidents(2)
                                        .totalAffectedCustomers(20)
                                        .totalRawIncidents(10)
                                        .totalRawAffectedCustomers(100)
                                        .uniqueIncidentsCount(0)
                                        .uniqueAffectedCustomers(0)
                                        .totalResources(30)
                                        .resources(Map.of("contractWorkers", 20, "foreignWorkers", 10))
                                        .windGustMph(50.0)
                                        .averageWindSpeed(5.0)
                                        .windGustThresholds(Map.of(
                                                "over20mph", 2,
                                                "over30mph", 2,
                                                "over40mph", 1))
                                        .totalRestorations(3)
                                        .totalAffectedCustomerRestorations(30)
                                        .cumulativeAffectedCustomers(50)
                                        .cumulativeRestorations(3)
                                        .cumulativeIncidents(5)
                                        .cumulativeCustomersRestored(30)
                                        .restoredPerWorker(0.1)
                                        .build()),
                        "System",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(previousTimestamp)
                                        .totalIncidents(15) // should be 15
                                        .totalAffectedCustomers(150)
                                        .totalRawIncidents(null)
                                        .totalRawAffectedCustomers(null)
                                        .uniqueIncidentsCount(15)
                                        .uniqueAffectedCustomers(150)
                                        .totalResources(100)
                                        .resources(Map.of("contractWorkers", 60, "foreignWorkers", 40))
                                        .windGustMph(40.0)
                                        .averageWindSpeed(9.0)
                                        .windGustThresholds(Map.of(
                                                "over20mph", 1,
                                                "over30mph", 1,
                                                "over40mph", 0))
                                        .totalRestorations(0)
                                        .totalAffectedCustomerRestorations(0)
                                        .cumulativeAffectedCustomers(150) // this one
                                        .cumulativeRestorations(0)
                                        .cumulativeIncidents(15)
                                        .cumulativeCustomersRestored(0)
                                        .restoredPerWorker(0.0)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(timestamp)
                                        .totalIncidents(14)
                                        .totalAffectedCustomers(140)
                                        .totalRawIncidents(20)
                                        .totalRawAffectedCustomers(200)
                                        .uniqueIncidentsCount(2)
                                        .uniqueAffectedCustomers(20)
                                        .totalResources(100)
                                        .resources(Map.of("contractWorkers", 60, "foreignWorkers", 40))
                                        .windGustMph(50.0)
                                        .averageWindSpeed(25.0)
                                        .windGustThresholds(Map.of(
                                                "over20mph", 2,
                                                "over30mph", 2,
                                                "over40mph", 1))
                                        .totalRestorations(1)
                                        .totalAffectedCustomerRestorations(10)
                                        .cumulativeAffectedCustomers(170)
                                        .cumulativeRestorations(1)
                                        .cumulativeIncidents(17)
                                        .cumulativeCustomersRestored(10)
                                        .restoredPerWorker(0.01)
                                        .build())))
                .startTime(previousTimestamp.minusMinutes(1))
                .endTime(timestamp.plusMinutes(1))
                .build();

        String expectedJson = objectMapper.writeValueAsString(expectedMetric);

        MvcResult result = mockMvc.perform(get("/overview/aggregate")
                        .param("start", expectedStart.toString())
                        .param("end", expectedEnd.toString())) // storm window is covered in the service test
                .andExpect(status().isOk())
                .andExpect(content().json(expectedJson))
                .andReturn();
    }

    private StormRecord createMockStormRecord() {
        return StormRecord.builder()
                .id("5f50c31e917a6a3a3e4cedf2")
                .stormName("TestStorm")
                .stormMode("active")
                .creationDate(ZonedDateTime.now(UTC))
                .build();
    }

    private List<ActiveOutages> createMockOutages(ZonedDateTime timestamp) {

        return List.of(
                ActiveOutages.builder()
                        .id(ACTIVE_OUTAGE_ID1)
                        .lastUpdated(timestamp)
                        .createdTimestamp(timestamp.minusHours(1))
                        .generatedBy("Test")
                        .outageRecords(List.of(
                                ActiveOutages.OutageRecord.builder()
                                        .region("Region1")
                                        .activeCustomerOutages(100)
                                        .activeIncidents(10)
                                        .rawActiveCustomerOutages(100)
                                        .rawActiveIncidents(10)
                                        .restoredCustomerOutages(0)
                                        .restoredIncidents(0)
                                        .build(),
                                ActiveOutages.OutageRecord.builder()
                                        .region("Region2")
                                        .activeCustomerOutages(50)
                                        .activeIncidents(5)
                                        .rawActiveCustomerOutages(100)
                                        .rawActiveIncidents(10)
                                        .restoredCustomerOutages(0)
                                        .restoredIncidents(0)
                                        .build(),
                                ActiveOutages.OutageRecord.builder()
                                        .region("System")
                                        .activeCustomerOutages(150)
                                        .activeIncidents(15)
                                        .rawActiveCustomerOutages(200)
                                        .rawActiveIncidents(20)
                                        .restoredCustomerOutages(0)
                                        .restoredIncidents(0)
                                        .build()))
                        .build(),
                ActiveOutages.builder()
                        .id(ACTIVE_OUTAGE_ID2)
                        .lastUpdated(timestamp)
                        .createdTimestamp(timestamp)
                        .generatedBy("Test")
                        .outageRecords(List.of(
                                ActiveOutages.OutageRecord.builder()
                                        .region("Region1")
                                        .activeCustomerOutages(120)
                                        .activeIncidents(12)
                                        .rawActiveCustomerOutages(100)
                                        .rawActiveIncidents(10)
                                        .restoredCustomerOutages(0)
                                        .restoredIncidents(0)
                                        .build(),
                                ActiveOutages.OutageRecord.builder()
                                        .region("Region2")
                                        .activeCustomerOutages(20)
                                        .activeIncidents(2)
                                        .rawActiveCustomerOutages(100)
                                        .rawActiveIncidents(10)
                                        .restoredCustomerOutages(30)
                                        .restoredIncidents(3)
                                        .build(),
                                ActiveOutages.OutageRecord.builder()
                                        .region("System")
                                        .activeCustomerOutages(140)
                                        .activeIncidents(14)
                                        .rawActiveCustomerOutages(200)
                                        .rawActiveIncidents(20)
                                        .restoredCustomerOutages(10)
                                        .restoredIncidents(1)
                                        .build()))
                        .build());
    }

    private ResourceEntity createMockResource() {
        return ResourceEntity.builder()
                .sessionId(new ObjectId())
                .stormStart(ZonedDateTime.now())
                .stormId("5f50c31e917a6a3a3e4cedf2")
                .creationTime(ZonedDateTime.now())
                .createdBy("Test")
                .generationType(ResourceEntity.ResourceConstants.GENERATION_TYPE_MANUAL)
                .systemResources(List.of(
                        ResourceEntity.SystemResources.builder()
                                .territoryId("Region1")
                                .territoryName("Region1")
                                .resources(Map.of("contractWorkers", 40, "foreignWorkers", 30))
                                .totalResources(70)
                                .resourcesTimeframes(List.of(ResourceEntity.ResourceTimeframe.builder()
                                        .arrivalTime(ZonedDateTime.now().minusHours(2))
                                        .resources(Map.of("contractWorkers", 40, "foreignWorkers", 30))
                                        .build()))
                                .build(),
                        ResourceEntity.SystemResources.builder()
                                .territoryId("Region2")
                                .territoryName("Region2")
                                .resources(Map.of("contractWorkers", 20, "foreignWorkers", 10))
                                .totalResources(30)
                                .resourcesTimeframes(List.of(ResourceEntity.ResourceTimeframe.builder()
                                        .arrivalTime(ZonedDateTime.now().minusHours(1))
                                        .resources(Map.of("contractWorkers", 20, "foreignWorkers", 10))
                                        .build()))
                                .build()))
                .build();
    }

    private Overview sortOverview(Overview overview) {
        Overview.OverviewData data = overview.getData();

        Map<String, Outage> sortedOutages = new LinkedHashMap<>();
        List<String> keyOrder = Arrays.asList("Region1", "Region2", "System");

        keyOrder.forEach(key -> {
            if (data.getOutages().containsKey(key)) {
                sortedOutages.put(key, data.getOutages().get(key));
            }
        });

        Map<String, Resource> sortedResources = new LinkedHashMap<>();
        keyOrder.forEach(key -> {
            if (data.getResources().containsKey(key)) {
                sortedResources.put(key, data.getResources().get(key));
            }
        });

        return Overview.builder()
                .timestamp(overview.getTimestamp())
                .data(Overview.OverviewData.builder()
                        .outages(sortedOutages)
                        .resources(sortedResources)
                        .build())
                .build();
    }

    private List<RawWeatherData> createMultipleRawWeatherData(
            ZonedDateTime previousTimestamp, ZonedDateTime currentTimestamp) {
        return List.of(
                createMockRawWeatherData(previousTimestamp, true), createMockRawWeatherData(currentTimestamp, false));
    }

    private RawWeatherData createMockRawWeatherData(ZonedDateTime timestamp, boolean isPrevious) {
        Map<String, RawWeatherData.WeatherStation> weatherStations = new HashMap<>();
        weatherStations.put(
                "Station1",
                RawWeatherData.WeatherStation.builder()
                        .stationId(UUID.randomUUID())
                        .latitude(33.6598)
                        .longitude(-85.8314)
                        .region("Region1")
                        .weatherAttributes(RawWeatherData.WeatherAttributes.builder()
                                .temperatureCelsius(isPrevious ? 25.0 : 27.0)
                                .humidityPercent(isPrevious ? 60.0 : 65.0)
                                .windBearingDeg(isPrevious ? 180 : 190)
                                .windGustMph(isPrevious ? 30.0 : 31.0)
                                .windSpeedMph(isPrevious ? 10.0 : 20.0)
                                .build())
                        .build());

        weatherStations.put(
                "Station2",
                RawWeatherData.WeatherStation.builder()
                        .stationId(UUID.randomUUID())
                        .latitude(33.5207)
                        .longitude(-86.8025)
                        .region("Region1")
                        .weatherAttributes(RawWeatherData.WeatherAttributes.builder()
                                .temperatureCelsius(isPrevious ? 10.0 : 15.0)
                                .humidityPercent(isPrevious ? 10.0 : 15.0)
                                .windBearingDeg(isPrevious ? 10 : 15)
                                .windGustMph(isPrevious ? 10.0 : 32.0)
                                .windSpeedMph(isPrevious ? 15.0 : 50.0)
                                .build())
                        .build());

        weatherStations.put(
                "Station3",
                RawWeatherData.WeatherStation.builder()
                        .stationId(UUID.randomUUID())
                        .latitude(34.5207)
                        .longitude(-81.8025)
                        .region("Region2")
                        .weatherAttributes(RawWeatherData.WeatherAttributes.builder()
                                .temperatureCelsius(isPrevious ? 28.0 : 30.0)
                                .humidityPercent(isPrevious ? 55.0 : 50.0)
                                .windBearingDeg(isPrevious ? 225 : 230)
                                .windGustMph(isPrevious ? 40.0 : 50.0)
                                .windSpeedMph(isPrevious ? 2.0 : 5.0)
                                .build())
                        .build());

        return RawWeatherData.builder()
                .timestamp(timestamp.truncatedTo(ChronoUnit.HOURS))
                .weatherStations(weatherStations)
                .build();
    }
}
