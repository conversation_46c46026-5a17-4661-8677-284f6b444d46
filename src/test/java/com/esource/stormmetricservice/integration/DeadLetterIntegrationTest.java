package com.esource.stormmetricservice.integration;

import java.time.Duration;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import com.esource.AbstractIntegrationTestContainer;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.stormevents.events.deadletter.model.DeadLetter;
import com.esource.stormmetricservice.stormevents.events.deadletter.repository.DeadLetterRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles(profiles = {"test", "disableSecurity", "disableSwagger"})
@TestPropertySource(properties = {"spring.application.name=Tim"})
class DeadLetterIntegrationTest extends AbstractIntegrationTestContainer {
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private DeadLetterRepository deadLetterRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String EXCHANGE_NAME = "storm.storm-event-exchange";
    private static final String ROUTING_KEY_FAULTY = "storm.storm-event.metric-service.test2";

    @BeforeEach
    void setup() {

        await().atMost(10, TimeUnit.SECONDS)
                .pollInterval(1, TimeUnit.SECONDS)
                .until(() -> rabbitTemplate.receive("storm.storm-event.metric-service.storm-end.queue") == null);

        await().atMost(10, TimeUnit.SECONDS)
                .pollInterval(1, TimeUnit.SECONDS)
                .until(() ->
                        rabbitTemplate.receive("storm.storm-event.metric-service.aggregate-overview.queue") == null);

        deadLetterRepository.deleteAll();
    }

    @Test
    void testMessageFailureHandling() {
        StormOverview stormData = StormOverview.builder()
                .id("invalid")
                .name("Test Storm")
                .startDate(ZonedDateTime.parse("2022-01-01T00:00:00Z"))
                .endDate(ZonedDateTime.parse("2022-01-02T00:00:00Z"))
                .build();

        EndStormEvent faultyEvent = EndStormEvent.builder()
                .timestamp(ZonedDateTime.now(ZoneOffset.UTC))
                .eventName("stormEndEvent")
                .stormData(stormData)
                .build();

        String serializedPayload;
        try {
            serializedPayload = objectMapper.writeValueAsString(faultyEvent);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            serializedPayload = "";
        }

        rabbitTemplate.convertAndSend(EXCHANGE_NAME, ROUTING_KEY_FAULTY, faultyEvent);

        List<DeadLetter> deadLetters =
                await().atMost(Duration.ofSeconds(10)).until(deadLetterRepository::findAll, list -> !list.isEmpty());

        assertTrue(!deadLetters.isEmpty(), "Dead letter should be created");
        DeadLetter deadLetter = deadLetters.get(0);

        assertEquals("Tim", deadLetter.getApp());
        assertEquals(EXCHANGE_NAME, deadLetter.getExchange());
        assertEquals(ROUTING_KEY_FAULTY, deadLetter.getRoutingKey());
        assertEquals(serializedPayload, deadLetter.getPayload());
        assertNotNull(deadLetter.getCause(), "Cause should not be null");
    }
}
