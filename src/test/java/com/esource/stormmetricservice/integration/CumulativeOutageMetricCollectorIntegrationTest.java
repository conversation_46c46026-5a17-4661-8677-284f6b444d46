package com.esource.stormmetricservice.integration;

import java.time.Duration;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import com.esource.AbstractIntegrationTestContainer;
import com.esource.stormmetricservice.aggregatemetrics.model.Overview;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.aggregatemetrics.repository.OverviewRepository;
import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.metrics.model.metrics.CumulativeOutageMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertEquals;

@SpringBootTest
@ActiveProfiles(profiles = {"test", "disableSecurity", "disableSwagger"})
@TestPropertySource(properties = {"rabbitmq.queue.stormEnd=storm.storm-event.metric-service.storm-end.queue.delta"})
class CumulativeOutageMetricCollectorIntegrationTest extends AbstractIntegrationTestContainer {

    @Autowired
    private MetricRepository metricRepository;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private OverviewRepository overviewRepository;

    @BeforeEach
    void setup() {
        await().atMost(10, TimeUnit.SECONDS)
                .pollInterval(1, TimeUnit.SECONDS)
                .until(() -> rabbitTemplate.receive("storm.storm-event.metric-service.storm-end.queue.delta") == null);
    }

    @AfterEach
    void tearDown() {
        await().atMost(10, TimeUnit.SECONDS)
                .until(() -> rabbitTemplate.receive("storm.storm-event.metric-service.storm-end.queue.delta") == null);
        metricRepository.deleteAll();
        overviewRepository.deleteAll();
    }

    @Test
    void testCumulativeOutageMetricCollection() {
        // Setup mock data
        EndStormEvent testEvent = createMockEndStormEvent();

        List<Overview> mockActiveOverviews = generateMockOverviews();
        overviewRepository.saveAll(mockActiveOverviews);

        StormMetric expectedMetric = createExpectedStormMetric();

        // Send the mock event to the RabbitMQ queue
        rabbitTemplate.convertAndSend("storm.storm-event-exchange", "storm.storm-event.metric-service.test", testEvent);

        StormMetric actualMetric = await().atMost(Duration.ofSeconds(1000))
                .until(
                        () -> metricRepository
                                .findByStormIdAndMetricType(
                                        new ObjectId(testEvent.getStormData().getId()),
                                        MetricConstants.METRIC_TYPE_CUMULATIVE_OUTAGES)
                                .orElse(null),
                        Objects::nonNull);

        assertEquals(
                expectedMetric.getMetrics().size(), actualMetric.getMetrics().size());

        assertEquals(expectedMetric.getMetrics(), actualMetric.getMetrics());
        assertEquals(expectedMetric.getStormId(), actualMetric.getStormId());
        assertEquals(expectedMetric.getMetricType(), actualMetric.getMetricType());
    }

    private EndStormEvent createMockEndStormEvent() {
        StormOverview stormData = StormOverview.builder()
                .id("5f50c31e917a6a3a3e4cedf5")
                .name("Test Storm")
                .startDate(ZonedDateTime.parse("2022-01-01T00:00:00Z"))
                .endDate(ZonedDateTime.parse("2022-01-02T00:00:00Z"))
                .build();

        return EndStormEvent.builder()
                .timestamp(java.time.ZonedDateTime.now(ZoneOffset.UTC))
                .eventName("stormEndEvent")
                .stormData(stormData)
                .build();
    }

    private List<Overview> generateMockOverviews() {
        Map<String, Outage> outagesByRegion1 = Map.ofEntries(
                Map.entry(
                        "Region1",
                        Outage.builder()
                                .totalIncidents(2)
                                .totalAffectedCustomers(200)
                                .uniqueIncidentsCount(2)
                                .uniqueAffectedCustomers(200)
                                .build()),
                Map.entry(
                        "Region2",
                        Outage.builder()
                                .totalIncidents(1)
                                .totalAffectedCustomers(100)
                                .uniqueIncidentsCount(1)
                                .uniqueAffectedCustomers(100)
                                .build()));
        Overview overview1 = Overview.builder()
                .timestamp(ZonedDateTime.parse("2022-01-01T01:00:00Z"))
                .data(Overview.OverviewData.builder()
                        .outages(outagesByRegion1)
                        .resources(Map.of())
                        .stationData(List.of())
                        .build())
                .build();

        Map<String, Outage> outagesByRegion2 = Map.ofEntries(
                Map.entry(
                        "Region1",
                        Outage.builder()
                                .totalIncidents(2)
                                .totalAffectedCustomers(210)
                                .uniqueIncidentsCount(2)
                                .uniqueAffectedCustomers(210)
                                .build()),
                Map.entry(
                        "Region2",
                        Outage.builder()
                                .totalIncidents(0)
                                .totalAffectedCustomers(0)
                                .uniqueIncidentsCount(0)
                                .uniqueAffectedCustomers(0)
                                .build()));
        Overview overview2 = Overview.builder()
                .timestamp(ZonedDateTime.parse("2022-01-01T04:00:00Z"))
                .data(Overview.OverviewData.builder()
                        .outages(outagesByRegion2)
                        .resources(Map.of())
                        .stationData(List.of())
                        .build())
                .build();

        return List.of(overview1, overview2);
    }

    private StormMetric createExpectedStormMetric() {
        return StormMetric.builder()
                .stormId(new ObjectId("5f50c31e917a6a3a3e4cedf5"))
                .metricType("cumulative-outages")
                .metrics(List.of(
                        CumulativeOutageMetric.builder()
                                .timestamp(ZonedDateTime.parse("2022-01-01T01:00:00Z"))
                                .records(List.of(
                                        CumulativeOutageMetric.CumulativeOutageRecord.builder()
                                                .region("Region1")
                                                .cumulativeCustomerOutages(200)
                                                .cumulativeIncidents(2)
                                                .cumulativeRestoredCustomerOutages(0)
                                                .cumulativeRestoredIncidents(0)
                                                .build(),
                                        CumulativeOutageMetric.CumulativeOutageRecord.builder()
                                                .region("Region2")
                                                .cumulativeCustomerOutages(100)
                                                .cumulativeIncidents(1)
                                                .cumulativeRestoredCustomerOutages(0)
                                                .cumulativeRestoredIncidents(0)
                                                .build()))
                                .build(),
                        CumulativeOutageMetric.builder()
                                .timestamp(ZonedDateTime.parse("2022-01-01T04:00:00Z"))
                                .records(List.of(
                                        CumulativeOutageMetric.CumulativeOutageRecord.builder()
                                                .region("Region1")
                                                .cumulativeCustomerOutages(410)
                                                .cumulativeIncidents(4)
                                                .cumulativeRestoredCustomerOutages(0)
                                                .cumulativeRestoredIncidents(0)
                                                .build(),
                                        CumulativeOutageMetric.CumulativeOutageRecord.builder()
                                                .region("Region2")
                                                .cumulativeCustomerOutages(100)
                                                .cumulativeIncidents(1)
                                                .cumulativeRestoredCustomerOutages(100)
                                                .cumulativeRestoredIncidents(1)
                                                .build()))
                                .build()))
                .build();
    }
}
