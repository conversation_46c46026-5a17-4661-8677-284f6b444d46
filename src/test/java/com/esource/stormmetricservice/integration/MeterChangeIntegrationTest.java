package com.esource.stormmetricservice.integration;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.util.UriComponentsBuilder;

import com.esource.AbstractIntegrationTestContainer;
import com.esource.stormmetricservice.ami.model.AmiMeterChangeDocument;
import com.esource.stormmetricservice.ami.model.MeterChangesResponseDto;
import com.esource.stormmetricservice.ami.repository.AmiChangeRepository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles(profiles = {"test", "disableSecurity", "disableSwagger"})
class MeterChangeIntegrationTest extends AbstractIntegrationTestContainer {

    @LocalServerPort
    private int port;

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private AmiChangeRepository amiChangeRepository;

    private static final String BASE_URL = "http://localhost:";
    private static final ZonedDateTime BASE_TIME = ZonedDateTime.now(ZoneOffset.UTC);

    @BeforeEach
    void setup() {
        amiChangeRepository.deleteAll();
        setupTestData();
    }

    private void setupTestData() {
        AmiMeterChangeDocument change1 = new AmiMeterChangeDocument();
        change1.setTimestamp(BASE_TIME.minusHours(2));
        change1.setMetersChanged(Arrays.asList(createMeterChange("meter1", 1), createMeterChange("meter2", 2)));

        AmiMeterChangeDocument change2 = new AmiMeterChangeDocument();
        change2.setTimestamp(BASE_TIME.minusHours(1));
        change2.setMetersChanged(Arrays.asList(createMeterChange("meter1", 3), createMeterChange("meter3", 1)));

        amiChangeRepository.saveAll(Arrays.asList(change1, change2));
    }

    private AmiMeterChangeDocument.MeterChange createMeterChange(String meterId, int status) {
        AmiMeterChangeDocument.MeterChange meterChange = new AmiMeterChangeDocument.MeterChange();
        meterChange.setMeterId(meterId);
        meterChange.setStatus(status);
        return meterChange;
    }

    @Test
    void testGetMeterChangesSuccess() {
        String url = UriComponentsBuilder.fromUriString(BASE_URL + port + "/meters/ami-replay")
                .queryParam("stormStart", BASE_TIME.minusHours(3).toString())
                .queryParam("stormEnd", BASE_TIME.toString())
                .toUriString();

        ResponseEntity<MeterChangesResponseDto> response =
                restTemplate.getForEntity(url, MeterChangesResponseDto.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());

        MeterChangesResponseDto responseBody = response.getBody();
        assertNotNull(responseBody);
        List<MeterChangesResponseDto.AmiMeterSession> sessions = responseBody.getMeterSessions();

        assertEquals(2, sessions.size());

        MeterChangesResponseDto.AmiMeterSession firstSession = sessions.get(0);
        Map<String, Integer> firstSessionMeters = firstSession.getMeters();
        assertEquals(2, firstSessionMeters.size());
        assertEquals(1, firstSessionMeters.get("meter1"));
        assertEquals(2, firstSessionMeters.get("meter2"));

        MeterChangesResponseDto.AmiMeterSession secondSession = sessions.get(1);
        Map<String, Integer> secondSessionMeters = secondSession.getMeters();
        assertEquals(2, secondSessionMeters.size());
        assertEquals(3, secondSessionMeters.get("meter1"));
        assertEquals(1, secondSessionMeters.get("meter3"));
    }

    @Test
    void testGetMeterChangesNoData() {
        String url = UriComponentsBuilder.fromUriString(BASE_URL + port + "/meters/ami-replay")
                .queryParam("stormStart", BASE_TIME.plusDays(1).toString())
                .queryParam("stormEnd", BASE_TIME.plusDays(2).toString())
                .toUriString();

        ResponseEntity<MeterChangesResponseDto> response =
                restTemplate.getForEntity(url, MeterChangesResponseDto.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getMeterSessions().isEmpty());
    }

    @Test
    void testGetMeterChangesMissingParameters() {
        String urlMissingStart = UriComponentsBuilder.fromUriString(BASE_URL + port + "/meters/ami-replay")
                .queryParam("stormEnd", BASE_TIME.toString())
                .toUriString();

        ResponseEntity<String> response = restTemplate.getForEntity(urlMissingStart, String.class);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void testGetMeterChangesInvalidDateFormat() {
        String urlInvalidDate = UriComponentsBuilder.fromUriString(BASE_URL + port + "/meters/ami-replay")
                .queryParam("stormStart", "invalid-date")
                .queryParam("stormEnd", BASE_TIME.toString())
                .toUriString();

        ResponseEntity<String> response = restTemplate.getForEntity(urlInvalidDate, String.class);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    void testGetMeterChangesStartAfterEnd() {
        String url = UriComponentsBuilder.fromUriString(BASE_URL + port + "/meters/ami-replay")
                .queryParam("stormStart", BASE_TIME.toString())
                .queryParam("stormEnd", BASE_TIME.minusDays(1).toString())
                .toUriString();

        ResponseEntity<MeterChangesResponseDto> response =
                restTemplate.getForEntity(url, MeterChangesResponseDto.class);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getMeterSessions().isEmpty());
    }
}
