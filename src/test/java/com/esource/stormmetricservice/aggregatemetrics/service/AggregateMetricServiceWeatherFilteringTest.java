package com.esource.stormmetricservice.aggregatemetrics.service;

import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.esource.stormmetricservice.aggregatemetrics.repository.OverviewRepository;
import com.esource.stormmetricservice.weather.model.RawWeatherData.WeatherAttributes;
import com.esource.stormmetricservice.weather.model.RawWeatherData.WeatherStation;
import com.fasterxml.jackson.databind.ObjectMapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class AggregateMetricServiceWeatherFilteringTest {

    @Mock
    private OverviewRepository overviewRepository;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private AggregateMetricService aggregateMetricService;

    private WeatherStation createWeatherStation(String region, String workgroup) {
        return WeatherStation.builder()
                .stationId(UUID.randomUUID())
                .latitude(33.7490)
                .longitude(-84.3880)
                .region(region)
                .workGroup(workgroup)
                .weatherAttributes(WeatherAttributes.builder()
                        .temperatureCelsius(20.0)
                        .humidityPercent(60.0)
                        .windBearingDeg(180)
                        .windGustMph(25.0)
                        .windSpeedMph(15.0)
                        .build())
                .build();
    }

    @BeforeEach
    void setUp() {
        // No setup needed for these unit tests
    }

    @Test
    void testDefaultRegionBasedFiltering() {
        // Set regionalFilter to default (region)
        ReflectionTestUtils.setField(aggregateMetricService, "regionalFilter", "region");

        WeatherStation station1 = createWeatherStation("Atlanta", "MERIDIAN ZONE");
        WeatherStation station2 = createWeatherStation("Savannah", "COASTAL ZONE");

        // Test getGroupingKey method via reflection
        String groupingKey1 =
                (String) ReflectionTestUtils.invokeMethod(aggregateMetricService, "getGroupingKey", station1);
        String groupingKey2 =
                (String) ReflectionTestUtils.invokeMethod(aggregateMetricService, "getGroupingKey", station2);

        assertEquals("Atlanta", groupingKey1);
        assertEquals("Savannah", groupingKey2);
    }

    @Test
    void testWorkgroupBasedFiltering() {
        // Set regionalFilter to workgroup
        ReflectionTestUtils.setField(aggregateMetricService, "regionalFilter", "workgroup");

        WeatherStation station1 = createWeatherStation("Atlanta", "MERIDIAN ZONE");
        WeatherStation station2 = createWeatherStation("Savannah", "COASTAL ZONE");

        // Test getGroupingKey method via reflection
        String groupingKey1 =
                (String) ReflectionTestUtils.invokeMethod(aggregateMetricService, "getGroupingKey", station1);
        String groupingKey2 =
                (String) ReflectionTestUtils.invokeMethod(aggregateMetricService, "getGroupingKey", station2);

        assertEquals("Meridian zone", groupingKey1);
        assertEquals("Coastal zone", groupingKey2);
    }

    @Test
    void testCapitalizeString() {
        // Test capitalizeString method via reflection
        String result1 =
                (String) ReflectionTestUtils.invokeMethod(aggregateMetricService, "capitalizeString", "MERIDIAN ZONE");
        String result2 =
                (String) ReflectionTestUtils.invokeMethod(aggregateMetricService, "capitalizeString", "coastal zone");
        String result3 =
                (String) ReflectionTestUtils.invokeMethod(aggregateMetricService, "capitalizeString", "Mixed Case");

        assertEquals("Meridian zone", result1);
        assertEquals("Coastal zone", result2);
        assertEquals("Mixed case", result3);
    }

    @Test
    void testMatchesRegionOrWorkgroupWithRegionFilter() {
        ReflectionTestUtils.setField(aggregateMetricService, "regionalFilter", "region");

        WeatherStation station = createWeatherStation("Atlanta", "MERIDIAN ZONE");

        boolean matches = (Boolean) ReflectionTestUtils.invokeMethod(
                aggregateMetricService, "matchesRegionOrWorkgroup", station, "Atlanta");
        boolean doesNotMatch = (Boolean) ReflectionTestUtils.invokeMethod(
                aggregateMetricService, "matchesRegionOrWorkgroup", station, "Savannah");

        assertEquals(true, matches);
        assertEquals(false, doesNotMatch);
    }

    @Test
    void testMatchesRegionOrWorkgroupWithWorkgroupFilter() {
        ReflectionTestUtils.setField(aggregateMetricService, "regionalFilter", "workgroup");

        WeatherStation station = createWeatherStation("Atlanta", "MERIDIAN ZONE");

        boolean matches = (Boolean) ReflectionTestUtils.invokeMethod(
                aggregateMetricService, "matchesRegionOrWorkgroup", station, "Meridian zone");
        boolean doesNotMatch = (Boolean) ReflectionTestUtils.invokeMethod(
                aggregateMetricService, "matchesRegionOrWorkgroup", station, "Coastal zone");

        assertEquals(true, matches);
        assertEquals(false, doesNotMatch);
    }
}
