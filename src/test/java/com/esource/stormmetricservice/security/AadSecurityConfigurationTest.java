package com.esource.stormmetricservice.security;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.oidc.StandardClaimNames;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.RequestPostProcessor;

import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.jwt;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc
@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.MOCK,
        classes = {WebApplication.class})
@ActiveProfiles({"security:aad", "test", "disableSwagger"})
class AadSecurityConfigurationTest {

    private static final SimpleGrantedAuthority VISITOR_ROLE = new SimpleGrantedAuthority("visitor");
    private static final SimpleGrantedAuthority ADMINISTRATOR_ROLE = new SimpleGrantedAuthority("administrator");

    private static final String VISITOR_USERNAME = "<EMAIL>";
    private static final String ADMINISTRATOR_USERNAME = "<EMAIL>";

    private static final RequestPostProcessor visitorToken = jwt().authorities(List.of(VISITOR_ROLE))
            .jwt(jwt -> jwt.claim(StandardClaimNames.PREFERRED_USERNAME, VISITOR_USERNAME));

    private static final RequestPostProcessor administratorToken = jwt().authorities(
                    List.of(VISITOR_ROLE, ADMINISTRATOR_ROLE))
            .jwt(jwt -> jwt.claim(StandardClaimNames.PREFERRED_USERNAME, ADMINISTRATOR_USERNAME));

    @Autowired
    private MockMvc api;

    @DynamicPropertySource
    static void registerProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.cloud.azure.active-directory.enabled", () -> "true");
        registry.add("spring.cloud.azure.active-directory.profile.tenant-id", () -> "tenant-id");
        registry.add("spring.cloud.azure.active-directory.credential.client-id", () -> "client-id");
        registry.add("spring.cloud.azure.active-directory.credential.client-secret", () -> "client-secret");
        registry.add("spring.rabbitmq.listener.simple.auto-startup", () -> "false");
    }

    @Test
    void givenRequestIsAnonymous_WhenGetUser_ThenUnauthorized() throws Exception {
        api.perform(get("/user")).andExpect(status().isUnauthorized());
    }

    @Test
    void givenRequestIsAuthorized_WhenGetUser_ThenOk() throws Exception {
        api.perform(get("/user").with(visitorToken)).andExpect(status().isOk());
    }

    @Test
    void givenUserHasVisitorRole_WhenGetAdmin_ThenForbidden() throws Exception {
        api.perform(get("/admin").with(visitorToken)).andExpect(status().isForbidden());
    }

    @Test
    void givenUserHasAdministratorRole_WhenGetAdmin_ThenOk() throws Exception {
        api.perform(get("/admin").with(administratorToken)).andExpect(status().isOk());
    }
}
