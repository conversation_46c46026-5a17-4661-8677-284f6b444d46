package com.esource.stormmetricservice.security;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@ComponentScan(basePackages = {"com.esource.stormmetricservice"})
public class WebApplication {

    public static void main(String[] args) {
        SpringApplication.run(WebApplication.class, args);
    }

    @RestController
    public static class UserController {

        @GetMapping(path = "/user")
        public String getUserMessage() {
            return "Hello!";
        }

        @PreAuthorize("hasAuthority('administrator')")
        @GetMapping(path = "/admin")
        public String getAdministratorMessage() {
            return "Hello, Administrator!";
        }
    }
}
