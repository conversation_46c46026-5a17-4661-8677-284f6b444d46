package com.esource.stormmetricservice.repository.etr;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.esource.AbstractTestContainer;
import com.esource.stormmetricservice.etr.model.ActiveOutages;
import com.esource.stormmetricservice.etr.repository.ActiveOutagesRepository;

class ActiveOutagesRepositoryTest extends AbstractTestContainer {

    @Autowired
    private ActiveOutagesRepository activeOutagesRepository;

    @AfterEach
    void tearDown() {
        activeOutagesRepository.deleteAll();
    }

    @Test
    void testFindByCreatedTimestampBetween() {
        ObjectId stormId = new ObjectId("64c906acdecd567c13149c6a");
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("UTC")).truncatedTo(java.time.temporal.ChronoUnit.SECONDS);

        ZonedDateTime nowMinusOneDay = now.minusDays(1);

        ZonedDateTime nowPlusOneDay = now.plusDays(1);

        ActiveOutages activeOutages = ActiveOutages.builder()
                .id(stormId.toHexString())
                .sessionId(stormId)
                .lastUpdated(now)
                .createdTimestamp(now)
                .outageRecords(List.of(ActiveOutages.OutageRecord.builder()
                        .region("maine")
                        .activeCustomerOutages(23)
                        .activeIncidents(2)
                        .restoredCustomerOutages(23)
                        .restoredIncidents(2)
                        .build()))
                .build();

        activeOutagesRepository.save(activeOutages);

        List<ActiveOutages> actualOutages =
                activeOutagesRepository.findByCreatedTimestampBetween(nowMinusOneDay, nowPlusOneDay);

        Assertions.assertEquals(List.of(activeOutages), actualOutages);
    }

    @Test
    void testFindByCreatedTimestampLess() {
        ObjectId stormId = new ObjectId("64c906acdecd567c13149c6a");
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("UTC")).truncatedTo(java.time.temporal.ChronoUnit.SECONDS);

        ZonedDateTime nowMinusOneDay = now.minusDays(1);
        ActiveOutages activeOutages = ActiveOutages.builder()
                .id(stormId.toHexString())
                .sessionId(stormId)
                .lastUpdated(now)
                .createdTimestamp(now)
                .outageRecords(List.of(ActiveOutages.OutageRecord.builder()
                        .region("maine")
                        .activeCustomerOutages(23)
                        .activeIncidents(2)
                        .restoredCustomerOutages(23)
                        .restoredIncidents(2)
                        .build()))
                .build();

        activeOutagesRepository.save(activeOutages);

        List<ActiveOutages> actualOutages = activeOutagesRepository.findByCreatedTimestampBetween(nowMinusOneDay, now);

        Assertions.assertEquals(List.of(), actualOutages);
    }
}
