package com.esource.stormmetricservice.repository.etr;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.esource.AbstractTestContainer;
import com.esource.stormmetricservice.etr.model.ETR;
import com.esource.stormmetricservice.etr.repository.ETRRepository;

class ETRRepositoryTest extends AbstractTestContainer {

    @Autowired
    private ETRRepository etrRepository;

    @AfterEach
    void tearDown() {
        etrRepository.deleteAll();
    }

    @Test
    void testFindByCreatedTimestampBetween() {
        String stormId = "64c906acdecd567c13149c6a";
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("UTC")).truncatedTo(java.time.temporal.ChronoUnit.SECONDS);

        ETR etr = ETR.builder()
                .sessionId(new ObjectId(stormId))
                .stormId(stormId)
                .generationTime(now)
                .etrType("result")
                .build();

        etrRepository.save(etr);

        List<ETR> etrs = etrRepository.findByStormIdAndEtrType(stormId, "result");
        Assertions.assertEquals(List.of(etr), etrs);

        List<ETR> notFoundByEtrType = etrRepository.findByStormIdAndEtrType(stormId, "storm");
        Assertions.assertEquals(List.of(), notFoundByEtrType);

        List<ETR> notFoundByStormId = etrRepository.findByStormIdAndEtrType("64c906acdecd567c13149c6b", "result");
        Assertions.assertEquals(List.of(), notFoundByStormId);

        List<ETR> notFoundByStormIdAndEtrType =
                etrRepository.findByStormIdAndEtrType("64c906acdecd567c13149c6b", "storm");
        Assertions.assertEquals(List.of(), notFoundByStormIdAndEtrType);
    }
}
