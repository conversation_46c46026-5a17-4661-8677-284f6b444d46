package com.esource.stormmetricservice.repository.metric;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.esource.AbstractTestContainer;
import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.metrics.model.metrics.OutageMetric;
import com.esource.stormmetricservice.metrics.model.metrics.RestorationMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;

import static org.junit.jupiter.api.Assertions.assertEquals;

class MetricRepositoryTests extends AbstractTestContainer {

    @Autowired
    private MetricRepository metricRepository;

    @AfterEach
    void tearDown() {
        metricRepository.deleteAll();
    }

    @Test
    void testFindByStormId() {
        ObjectId stormId = new ObjectId("64c906acdecd567c13149c6a");
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("UTC")).truncatedTo(java.time.temporal.ChronoUnit.SECONDS);
        ZonedDateTime nowPlusOneDay = now.plusDays(1);

        StormOverview stormData = StormOverview.builder()
                .id(stormId.toHexString())
                .startDate(now)
                .endDate(nowPlusOneDay)
                .name("Test-Storm")
                .build();

        OutageMetric.OutageRecord record1 = OutageMetric.OutageRecord.builder()
                .region("maine")
                .activeCustomerOutages(23)
                .activeIncidents(2)
                .build();

        OutageMetric metric =
                OutageMetric.builder().timestamp(now).records(List.of(record1)).build();

        RestorationMetric.RestorationRecord record2 = RestorationMetric.RestorationRecord.builder()
                .region("maine")
                .restoredCustomerOutages(23)
                .restoredIncidents(2)
                .build();

        RestorationMetric metric2 = RestorationMetric.builder()
                .timestamp(now)
                .records(List.of(record2))
                .build();

        StormMetric stormMetric = StormMetric.builder()
                .stormId(stormId)
                .metricType("testType")
                .stormData(stormData)
                .lastUpdated(now)
                .metrics(List.of(metric, metric2))
                .build();

        metricRepository.save(stormMetric);

        StormMetric foundMetric = metricRepository.findByStormId(stormId).orElseThrow();

        assertEquals(stormMetric, foundMetric);
    }
}
