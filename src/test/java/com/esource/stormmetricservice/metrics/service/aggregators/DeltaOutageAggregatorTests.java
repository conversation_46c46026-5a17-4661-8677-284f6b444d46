package com.esource.stormmetricservice.metrics.service.aggregators;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.aggregatemetrics.service.aggregators.DeltaOutageAggregator;
import com.esource.stormmetricservice.etr.model.ActiveOutages;
import com.esource.stormmetricservice.etr.repository.ActiveOutagesRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEvent;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DeltaOutageAggregatorTests {

    @Mock
    private ActiveOutagesRepository activeOutagesRepository;

    @InjectMocks
    private DeltaOutageAggregator deltaOutageAggregator;

    @Test
    void testAggregateOutages_WithPreviousOutage() {
        // Arrange
        ZonedDateTime timestamp = ZonedDateTime.now();
        AggregateOverviewEvent event = new AggregateOverviewEvent();
        event.setTimestamp(timestamp);
        event.setMetadata(Map.of("activeOutageId", "507f1f77bcf86cd799439010"));

        ActiveOutages currentOutage = ActiveOutages.builder()
                .id("507f1f77bcf86cd799439010")
                .createdTimestamp(timestamp)
                .outageRecords(List.of(
                        ActiveOutages.OutageRecord.builder()
                                .region("Region1")
                                .activeIncidents(10)
                                .activeCustomerOutages(100)
                                .build(),
                        ActiveOutages.OutageRecord.builder()
                                .region("Region2")
                                .activeIncidents(5)
                                .activeCustomerOutages(50)
                                .build()))
                .build();

        ActiveOutages previousOutage = ActiveOutages.builder()
                .id("507f1f77bcf86cd799439011")
                .createdTimestamp(timestamp.minusMinutes(5))
                .outageRecords(List.of(
                        ActiveOutages.OutageRecord.builder()
                                .region("Region1")
                                .activeIncidents(6)
                                .activeCustomerOutages(80)
                                .build(),
                        ActiveOutages.OutageRecord.builder()
                                .region("Region2")
                                .activeIncidents(3)
                                .activeCustomerOutages(30)
                                .build()))
                .build();

        when(activeOutagesRepository.findFirstByCreatedTimestampLessThanEqualOrderByCreatedTimestampDesc(
                        any(ZonedDateTime.class)))
                .thenReturn(Optional.of(currentOutage));

        when(activeOutagesRepository.findFirstByCreatedTimestampLessThanOrderByCreatedTimestampDesc(
                        any(ZonedDateTime.class)))
                .thenReturn(Optional.of(previousOutage));

        // Act
        Map<String, Outage> result = deltaOutageAggregator.aggregateOutages(event);

        // Assert
        assertEquals(3, result.size());

        Outage region1Outage = result.get("Region1");
        assertEquals(10, region1Outage.getTotalIncidents());
        assertEquals(100, region1Outage.getTotalAffectedCustomers());
        assertEquals(4, region1Outage.getUniqueIncidentsCount());
        assertEquals(20, region1Outage.getUniqueAffectedCustomers());

        Outage region2Outage = result.get("Region2");
        assertEquals(5, region2Outage.getTotalIncidents());
        assertEquals(50, region2Outage.getTotalAffectedCustomers());
        assertEquals(2, region2Outage.getUniqueIncidentsCount());
        assertEquals(20, region2Outage.getUniqueAffectedCustomers());
    }

    @Test
    void testAggregateOutages_WithoutPreviousOutage() {
        // Arrange
        ZonedDateTime timestamp = ZonedDateTime.now();
        AggregateOverviewEvent event = new AggregateOverviewEvent();
        event.setTimestamp(timestamp);
        event.setMetadata(Map.of("activeOutageId", "507f1f77bcf86cd799439010"));

        ActiveOutages currentOutage = ActiveOutages.builder()
                .createdTimestamp(timestamp)
                .outageRecords(List.of(
                        ActiveOutages.OutageRecord.builder()
                                .region("Region1")
                                .activeIncidents(10)
                                .activeCustomerOutages(100)
                                .build(),
                        ActiveOutages.OutageRecord.builder()
                                .region("Region2")
                                .activeIncidents(5)
                                .activeCustomerOutages(50)
                                .build()))
                .build();

        when(activeOutagesRepository.findFirstByCreatedTimestampLessThanEqualOrderByCreatedTimestampDesc(
                        any(ZonedDateTime.class)))
                .thenReturn(Optional.of(currentOutage));

        when(activeOutagesRepository.findFirstByCreatedTimestampLessThanOrderByCreatedTimestampDesc(
                        any(ZonedDateTime.class)))
                .thenReturn(Optional.empty());

        // Act
        Map<String, Outage> result = deltaOutageAggregator.aggregateOutages(event);

        // Assert
        assertEquals(3, result.size());

        Outage region1Outage = result.get("Region1");
        assertEquals(10, region1Outage.getTotalIncidents());
        assertEquals(100, region1Outage.getTotalAffectedCustomers());
        assertEquals(10, region1Outage.getUniqueIncidentsCount());
        assertEquals(100, region1Outage.getUniqueAffectedCustomers());

        Outage region2Outage = result.get("Region2");
        assertEquals(5, region2Outage.getTotalIncidents());
        assertEquals(50, region2Outage.getTotalAffectedCustomers());
        assertEquals(5, region2Outage.getUniqueIncidentsCount());
        assertEquals(50, region2Outage.getUniqueAffectedCustomers());

        Outage systemOutage = result.get("System");
        assertEquals(15, systemOutage.getTotalIncidents());
        assertEquals(150, systemOutage.getTotalAffectedCustomers());
        assertEquals(15, systemOutage.getUniqueIncidentsCount());
        assertEquals(150, systemOutage.getUniqueAffectedCustomers());
    }
}
