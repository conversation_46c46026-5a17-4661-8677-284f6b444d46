package com.esource.stormmetricservice.metrics.service;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.model.metrics.*;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MetricServiceTest {

    @Mock
    private MetricRepository metricRepository;

    @InjectMocks
    private MetricService metricService;

    @Test
    void testGetOutageMetricByStormId_filterBySystemTrue() {
        String stormId = "6466b4577ec2164a25e964ae";
        OutageMetric.OutageRecord outageRecordWithSystem = OutageMetric.OutageRecord.builder()
                .region("System")
                .activeCustomerOutages(23)
                .activeIncidents(2)
                .build();
        OutageMetric.OutageRecord outageRecordWithRegion = OutageMetric.OutageRecord.builder()
                .region("Region1")
                .activeCustomerOutages(10)
                .activeIncidents(1)
                .build();
        OutageMetric outageMetrics = OutageMetric.builder()
                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(outageRecordWithSystem, outageRecordWithRegion))
                .build();
        OutageMetric outageMetrics2 = OutageMetric.builder()
                .timestamp(ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(outageRecordWithSystem, outageRecordWithRegion))
                .build();
        StormMetric stormMetric = StormMetric.builder()
                .metricType("outages")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(outageMetrics, outageMetrics2))
                .build();

        when(metricRepository.findByStormIdAndMetricType(new ObjectId("6466b4577ec2164a25e964ae"), "outages"))
                .thenReturn(Optional.of(stormMetric));

        Optional<StormMetric> stormMetricActual = metricService.getMetricByStormIdAndType(stormId, "outages", true);

        Optional<StormMetric> stormMetricExpected = Optional.of(StormMetric.builder()
                .metricType("outages")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(
                        OutageMetric.builder()
                                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                                .records(List.of(outageRecordWithSystem))
                                .build(),
                        OutageMetric.builder()
                                .timestamp(ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                                .records(List.of(outageRecordWithSystem))
                                .build()))
                .build());
        Assertions.assertTrue(stormMetricActual.isPresent());
        Assertions.assertEquals(stormMetricExpected.get(), stormMetricActual.get());
    }

    @Test
    void testGetCumulativeOutageMetricByStormId_filterBySystemTrue() {
        String stormId = "6466b4577ec2164a25e964ae";
        CumulativeOutageMetric.CumulativeOutageRecord outageRecordWithSystem =
                CumulativeOutageMetric.CumulativeOutageRecord.builder()
                        .region("System")
                        .cumulativeCustomerOutages(23)
                        .cumulativeIncidents(2)
                        .cumulativeRestoredCustomerOutages(10)
                        .cumulativeRestoredIncidents(1)
                        .build();
        CumulativeOutageMetric.CumulativeOutageRecord outageRecordWithRegion =
                CumulativeOutageMetric.CumulativeOutageRecord.builder()
                        .region("Region1")
                        .cumulativeCustomerOutages(23)
                        .cumulativeIncidents(2)
                        .cumulativeRestoredCustomerOutages(10)
                        .cumulativeRestoredIncidents(1)
                        .build();
        CumulativeOutageMetric outageMetrics = CumulativeOutageMetric.builder()
                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(outageRecordWithSystem, outageRecordWithRegion))
                .build();
        CumulativeOutageMetric outageMetrics2 = CumulativeOutageMetric.builder()
                .timestamp(ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(outageRecordWithSystem, outageRecordWithRegion))
                .build();
        StormMetric stormMetric = StormMetric.builder()
                .metricType("cumulative-outages")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(outageMetrics, outageMetrics2))
                .build();

        when(metricRepository.findByStormIdAndMetricType(
                        new ObjectId("6466b4577ec2164a25e964ae"), "cumulative-outages"))
                .thenReturn(Optional.of(stormMetric));

        Optional<StormMetric> stormMetricActual =
                metricService.getMetricByStormIdAndType(stormId, "cumulative-outages", true);

        Optional<StormMetric> stormMetricExpected = Optional.of(StormMetric.builder()
                .metricType("cumulative-outages")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(
                        CumulativeOutageMetric.builder()
                                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                                .records(List.of(outageRecordWithSystem))
                                .build(),
                        CumulativeOutageMetric.builder()
                                .timestamp(ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                                .records(List.of(outageRecordWithSystem))
                                .build()))
                .build());
        Assertions.assertTrue(stormMetricActual.isPresent());
        Assertions.assertEquals(stormMetricExpected.get(), stormMetricActual.get());
    }

    @Test
    void testGetOutageMetricByStormId_filterBySystemFalse() {
        String stormId = "6466b4577ec2164a25e964ae";
        OutageMetric.OutageRecord outageRecordWithSystem = OutageMetric.OutageRecord.builder()
                .region("System")
                .activeCustomerOutages(23)
                .activeIncidents(2)
                .build();
        OutageMetric.OutageRecord outageRecordWithRegion = OutageMetric.OutageRecord.builder()
                .region("Region1")
                .activeCustomerOutages(10)
                .activeIncidents(1)
                .build();
        OutageMetric outageMetrics = OutageMetric.builder()
                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(outageRecordWithSystem, outageRecordWithRegion))
                .build();
        OutageMetric outageMetrics2 = OutageMetric.builder()
                .timestamp(ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(outageRecordWithSystem, outageRecordWithRegion))
                .build();
        StormMetric stormMetric = StormMetric.builder()
                .metricType("outages")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(outageMetrics, outageMetrics2))
                .build();

        when(metricRepository.findByStormIdAndMetricType(new ObjectId("6466b4577ec2164a25e964ae"), "outages"))
                .thenReturn(Optional.of(stormMetric));

        Optional<StormMetric> stormMetricActual = metricService.getMetricByStormIdAndType(stormId, "outages", false);

        Optional<StormMetric> stormMetricExpected = Optional.of(StormMetric.builder()
                .metricType("outages")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(
                        OutageMetric.builder()
                                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                                .records(List.of(outageRecordWithSystem, outageRecordWithRegion))
                                .build(),
                        OutageMetric.builder()
                                .timestamp(ZonedDateTime.of(2022, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                                .records(List.of(outageRecordWithSystem, outageRecordWithRegion))
                                .build()))
                .build());
        Assertions.assertTrue(stormMetricActual.isPresent());
        Assertions.assertEquals(stormMetricExpected.get(), stormMetricActual.get());
    }

    @Test
    void testGetRestorationMetricByStormId_filterBySystemTrue() {
        String stormId = "6466b4577ec2164a25e964ae";
        RestorationMetric.RestorationRecord restorationRecordWithSystem = RestorationMetric.RestorationRecord.builder()
                .region("System")
                .restoredCustomerOutages(30)
                .restoredIncidents(3)
                .build();
        RestorationMetric.RestorationRecord restorationRecordWithRegion = RestorationMetric.RestorationRecord.builder()
                .region("Region1")
                .restoredCustomerOutages(15)
                .restoredIncidents(2)
                .build();
        RestorationMetric restorationMetrics = RestorationMetric.builder()
                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(restorationRecordWithSystem, restorationRecordWithRegion))
                .build();
        StormMetric stormMetric = StormMetric.builder()
                .metricType("restorations")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(restorationMetrics))
                .build();

        when(metricRepository.findByStormIdAndMetricType(new ObjectId(stormId), "restorations"))
                .thenReturn(Optional.of(stormMetric));

        Optional<StormMetric> stormMetricActual =
                metricService.getMetricByStormIdAndType(stormId, "restorations", true);

        Optional<StormMetric> stormMetricExpected = Optional.of(StormMetric.builder()
                .metricType("restorations")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(RestorationMetric.builder()
                        .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                        .records(List.of(restorationRecordWithSystem))
                        .build()))
                .build());

        Assertions.assertTrue(stormMetricActual.isPresent());
        Assertions.assertEquals(stormMetricExpected.get(), stormMetricActual.get());
    }

    @Test
    void testGetPredictionMetricByStormId_filterBySystemFalse() {
        String stormId = "6466b4577ec2164a25e964ae";
        PredictionMetric.PredictionRecord predictionRecordWithSystem = PredictionMetric.PredictionRecord.builder()
                .region("System")
                .hoursToRestoration(5.5)
                .type("TypeA")
                .build();
        PredictionMetric.PredictionRecord predictionRecordWithRegion = PredictionMetric.PredictionRecord.builder()
                .region("Region1")
                .hoursToRestoration(8.0)
                .type("TypeB")
                .build();
        PredictionMetric predictionMetrics = PredictionMetric.builder()
                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(predictionRecordWithSystem, predictionRecordWithRegion))
                .build();
        StormMetric stormMetric = StormMetric.builder()
                .metricType("medium-predicted-restorations")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(predictionMetrics))
                .build();

        when(metricRepository.findByStormIdAndMetricType(new ObjectId(stormId), "medium-predicted-restorations"))
                .thenReturn(Optional.of(stormMetric));

        Optional<StormMetric> stormMetricActual =
                metricService.getMetricByStormIdAndType(stormId, "medium-predicted-restorations", false);

        Optional<StormMetric> stormMetricExpected = Optional.of(StormMetric.builder()
                .metricType("medium-predicted-restorations")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(PredictionMetric.builder()
                        .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                        .records(List.of(predictionRecordWithSystem, predictionRecordWithRegion))
                        .build()))
                .build());

        Assertions.assertTrue(stormMetricActual.isPresent());
        Assertions.assertEquals(stormMetricExpected.get(), stormMetricActual.get());
    }

    @Test
    void testGetResourceMetricByStormId_filterBySystemFalse() {
        String stormId = "6466b4577ec2164a25e964ae";
        ResourceMetric.ResourceRecord resourceRecordWithSystem = ResourceMetric.ResourceRecord.builder()
                .region("System")
                .totalWorkers(100.0)
                .resources(Map.of("a", 10, "b", 90))
                .build();
        ResourceMetric.ResourceRecord resourceRecordWithRegion = ResourceMetric.ResourceRecord.builder()
                .region("Region1")
                .totalWorkers(60.0)
                .resources(Map.of("a", 10, "b", 50))
                .build();
        ResourceMetric resourceMetrics = ResourceMetric.builder()
                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(resourceRecordWithSystem, resourceRecordWithRegion))
                .build();
        StormMetric stormMetric = StormMetric.builder()
                .metricType("resources")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(resourceMetrics))
                .build();

        when(metricRepository.findByStormIdAndMetricType(new ObjectId(stormId), "resources"))
                .thenReturn(Optional.of(stormMetric));

        Optional<StormMetric> stormMetricActual = metricService.getMetricByStormIdAndType(stormId, "resources", false);

        Optional<StormMetric> stormMetricExpected = Optional.of(StormMetric.builder()
                .metricType("resources")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(ResourceMetric.builder()
                        .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                        .records(List.of(resourceRecordWithSystem, resourceRecordWithRegion))
                        .build()))
                .build());

        Assertions.assertTrue(stormMetricActual.isPresent());
        Assertions.assertEquals(stormMetricExpected.get(), stormMetricActual.get());
    }

    @Test
    void testGetResourceMetricByStormId_filterBySystemTrue() {
        String stormId = "6466b4577ec2164a25e964ae";
        ResourceMetric.ResourceRecord resourceRecordWithSystem = ResourceMetric.ResourceRecord.builder()
                .region("System")
                .totalWorkers(100.0)
                .resources(Map.of("a", 10, "b", 90))
                .build();
        ResourceMetric.ResourceRecord resourceRecordWithRegion = ResourceMetric.ResourceRecord.builder()
                .region("Region1")
                .totalWorkers(60.0)
                .resources(Map.of("a", 10, "b", 50))
                .build();
        ResourceMetric resourceMetrics = ResourceMetric.builder()
                .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                .records(List.of(resourceRecordWithSystem, resourceRecordWithRegion))
                .build();
        StormMetric stormMetric = StormMetric.builder()
                .metricType("resources")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(resourceMetrics))
                .build();

        when(metricRepository.findByStormIdAndMetricType(new ObjectId(stormId), "resources"))
                .thenReturn(Optional.of(stormMetric));

        Optional<StormMetric> stormMetricActual = metricService.getMetricByStormIdAndType(stormId, "resources", true);

        Optional<StormMetric> stormMetricExpected = Optional.of(StormMetric.builder()
                .metricType("resources")
                .stormId(new ObjectId(stormId))
                .metrics(List.of(ResourceMetric.builder()
                        .timestamp(ZonedDateTime.of(2021, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC))
                        .records(List.of(resourceRecordWithSystem))
                        .build()))
                .build());

        Assertions.assertTrue(stormMetricActual.isPresent());
        Assertions.assertEquals(stormMetricExpected.get(), stormMetricActual.get());
    }
}
