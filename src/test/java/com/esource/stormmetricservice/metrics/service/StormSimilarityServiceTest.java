package com.esource.stormmetricservice.metrics.service;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.esource.stormmetricservice.etr.model.StormRecord;
import com.esource.stormmetricservice.etr.repository.StormRecordRepository;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.metrics.model.StormRetrospective;
import com.esource.stormmetricservice.metrics.model.StormRetrospective.SimilarStorm;
import com.esource.stormmetricservice.metrics.model.StormRetrospective.StormEndSummary;
import com.esource.stormmetricservice.metrics.repository.StormRetrospectiveRepository;
import com.esource.stormmetricservice.metrics.service.similarity.DefaultStormSimilarityEvaluator;
import com.esource.stormmetricservice.metrics.service.similarity.StormSimilarityEvaluator;
import com.esource.stormmetricservice.metrics.service.similarity.StormSimilarityService;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class StormSimilarityServiceTest {

    @Mock
    private StormRetrospectiveRepository stormRetrospectiveRepository;

    @Mock
    private StormSimilarityEvaluator similarityEvaluator;

    @Mock
    private StormRecordRepository stormRecordRepository;

    @InjectMocks
    private StormSimilarityService stormSimilarityService;

    private StormRetrospective storm1;
    private StormRetrospective storm2;
    private StormRecord stormRecord1;
    private StormRecord stormRecord2;

    @BeforeEach
    void setUp() {
        StormOverview overview1 = StormOverview.builder()
                .id("1")
                .startDate(ZonedDateTime.now().minusDays(5))
                .endDate(ZonedDateTime.now().minusDays(3))
                .name("Storm 1")
                .build();
        StormOverview overview2 = StormOverview.builder()
                .id("2")
                .startDate(ZonedDateTime.now().minusDays(4))
                .endDate(ZonedDateTime.now().minusDays(2))
                .name("Storm 2")
                .build();

        Map<String, StormEndSummary> summary1 = new HashMap<>();
        summary1.put(
                "System",
                StormEndSummary.builder()
                        .totalCumulativeIncidents(100.0)
                        .totalCumulativeCustomersAffected(200.0)
                        .windGustThresholds(Map.of(
                                StormRetrospective.WIND_GUST_OVER_20,
                                10,
                                StormRetrospective.WIND_GUST_OVER_30,
                                5,
                                StormRetrospective.WIND_GUST_OVER_40,
                                2))
                        .averageWindSpeed(20.0)
                        .build());

        Map<String, StormEndSummary> summary2 = new HashMap<>();
        summary2.put(
                "System",
                StormEndSummary.builder()
                        .totalCumulativeIncidents(110.0)
                        .totalCumulativeCustomersAffected(190.0)
                        .windGustThresholds(Map.of(
                                StormRetrospective.WIND_GUST_OVER_20,
                                9,
                                StormRetrospective.WIND_GUST_OVER_30,
                                4,
                                StormRetrospective.WIND_GUST_OVER_40,
                                1))
                        .averageWindSpeed(18.0)
                        .build());

        storm1 = StormRetrospective.builder()
                .id("1")
                .stormId("1")
                .stormOverview(overview1)
                .stormEndSummary(summary1)
                .build();
        storm2 = StormRetrospective.builder()
                .id("2")
                .stormId("2")
                .stormOverview(overview2)
                .stormEndSummary(summary2)
                .build();

        storm1 = StormRetrospective.builder()
                .id("1")
                .stormId("1")
                .stormOverview(overview1)
                .stormEndSummary(summary1)
                .build();
        storm2 = StormRetrospective.builder()
                .id("2")
                .stormId("2")
                .stormOverview(overview2)
                .stormEndSummary(summary2)
                .build();
        stormRecord1 = StormRecord.builder()
                .id(storm1.getStormId())
                .stormName("storm1")
                .stormMode("archive")
                .creationDate(ZonedDateTime.now())
                .build();
        stormRecord2 = StormRecord.builder()
                .id(storm2.getStormId())
                .stormName("storm2")
                .stormMode("archive")
                .creationDate(ZonedDateTime.now())
                .build();
    }

    @Test
    void testCalculateStormSimilarities() {
        List<StormRetrospective> storms = Arrays.asList(storm1, storm2);

        // Mock repository
        when(stormRetrospectiveRepository.findAll()).thenReturn(storms);
        when(similarityEvaluator.isSimilar(storm1, storm2, StormRetrospective.DEFAULT_THRESHOLD))
                .thenReturn(true);
        when(similarityEvaluator.isSimilar(storm2, storm1, StormRetrospective.DEFAULT_THRESHOLD))
                .thenReturn(true);
        when(stormRecordRepository.findByStormMode("archive")).thenReturn(List.of(stormRecord1, stormRecord2));

        // Execute the service
        stormSimilarityService.calculateStormSimilarities();

        // Verify the interactions and results
        verify(stormRetrospectiveRepository, times(2)).save(any(StormRetrospective.class));

        List<SimilarStorm> similarStorms1 = storm1.getSimilarStorms();
        List<SimilarStorm> similarStorms2 = storm2.getSimilarStorms();

        assertNotNull(similarStorms1);
        assertNotNull(similarStorms2);
        assertEquals(1, similarStorms1.size());
        assertEquals(1, similarStorms2.size());
        assertEquals("2", similarStorms1.get(0).getStormId());
        assertEquals("1", similarStorms2.get(0).getStormId());
    }

    @Test
    void testCalculateSimilarity() {
        // Inject mocks
        DefaultStormSimilarityEvaluator evaluator = new DefaultStormSimilarityEvaluator();

        // Execute the method
        boolean isSimilar = evaluator.isSimilar(storm1, storm2, 0.01);

        assertEquals(true, isSimilar);
    }
}
