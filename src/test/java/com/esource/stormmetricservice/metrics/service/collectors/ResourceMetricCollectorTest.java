package com.esource.stormmetricservice.metrics.service.collectors;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.etr.model.ETR;
import com.esource.stormmetricservice.etr.repository.ETRRepository;
import com.esource.stormmetricservice.metrics.model.Metric;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.metrics.model.metrics.ResourceMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.when;

class ResourceMetricCollectorTest {

    @Mock
    private MetricRepository metricRepository;

    @Mock
    private ETRRepository etrRepository;

    @InjectMocks
    private ResourceMetricCollector resourceMetricCollector;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getMetricType() {
        assertEquals("resources", resourceMetricCollector.getMetricType());
    }

    @Test
    void testCollectMetricWithEmptyResources() {
        String stormId = "testStormId1";
        ETR mockETR = mockETRWithEmptyResources(stormId);
        when(etrRepository.findByStormIdAndEtrType(stormId, MetricConstants.ETR_RESULT))
                .thenReturn(List.of(mockETR));

        List<Metric> metrics = resourceMetricCollector.collectMetric(createMockEndStormEvent(stormId));

        assertFalse(metrics.isEmpty(), "Metrics list should be empty for ETR with empty resources");
        List<Metric> expectedMetrics = List.of(ResourceMetric.builder()
                .sessionId(mockETR.getSessionId().toHexString())
                .timestamp(mockETR.getGenerationTime())
                .records(List.of(ResourceMetric.ResourceRecord.builder()
                        .resources(Map.of("workerA", 2, "workerB", 4))
                        .totalWorkers(6.0)
                        .region("System")
                        .build()))
                .build());
        assertEquals(expectedMetrics, metrics, "Metrics list should match the expected metrics");
    }

    @Test
    void testCollectMetricWithNullResources() {
        String stormId = "testStormId2";
        ETR mockETR = mockETRWithNullResources(stormId);
        when(etrRepository.findByStormIdAndEtrType(stormId, MetricConstants.ETR_RESULT))
                .thenReturn(List.of(mockETR));

        List<Metric> metrics = resourceMetricCollector.collectMetric(createMockEndStormEvent(stormId));

        assertFalse(metrics.isEmpty(), "Metrics list should be empty for ETR with null resources");
        List<Metric> expectedMetrics = List.of(ResourceMetric.builder()
                .sessionId(mockETR.getSessionId().toHexString())
                .timestamp(mockETR.getGenerationTime())
                .records(List.of(ResourceMetric.ResourceRecord.builder()
                        .resources(Map.of("workerA", 2, "workerB", 4))
                        .totalWorkers(6.0)
                        .region("System")
                        .build()))
                .build());
        assertEquals(expectedMetrics, metrics, "Metrics list should match the expected metrics");
    }

    @Test
    void testCollectMetricWithSystemResources() {
        String stormId = "testStormId3";
        ETR mockETR = mockETRWithSystemResources(stormId);
        when(etrRepository.findByStormIdAndEtrType(stormId, MetricConstants.ETR_RESULT))
                .thenReturn(List.of(mockETR));

        List<Metric> metrics = resourceMetricCollector.collectMetric(createMockEndStormEvent(stormId));

        assertFalse(metrics.isEmpty(), "Metrics list should not be empty for ETR with system resources");
        List<Metric> expectedMetrics = List.of(ResourceMetric.builder()
                .sessionId(mockETR.getSessionId().toHexString())
                .timestamp(mockETR.getGenerationTime())
                .records(List.of(ResourceMetric.ResourceRecord.builder()
                        .resources(Map.of("workerA", 10, "workerB", 12))
                        .totalWorkers(22.0)
                        .region("System")
                        .build()))
                .build());
        assertEquals(expectedMetrics, metrics, "Metrics list should match the expected metrics");
        // Further assertions can be added to verify the content of the metrics
    }

    @Test
    void testCollectMetricWithOutSystemResources() {
        String stormId = "testStormId4";
        ETR mockETR = mockETRWithOutSystemResources(stormId);
        when(etrRepository.findByStormIdAndEtrType(stormId, MetricConstants.ETR_RESULT))
                .thenReturn(List.of(mockETR));

        List<Metric> metrics = resourceMetricCollector.collectMetric(createMockEndStormEvent(stormId));

        assertFalse(metrics.isEmpty(), "Metrics list should not be empty for ETR without system region resources");
        List<Metric> expectedMetrics = List.of(ResourceMetric.builder()
                .sessionId(mockETR.getSessionId().toHexString())
                .timestamp(mockETR.getGenerationTime())
                .records(List.of(
                        ResourceMetric.ResourceRecord.builder()
                                .resources(Map.of("workerA", 10, "workerB", 12))
                                .totalWorkers(22.0)
                                .region("Edison")
                                .build(),
                        ResourceMetric.ResourceRecord.builder()
                                .resources(Map.of("workerA", 11, "workerB", 13))
                                .totalWorkers(24.0)
                                .region("McKeesport")
                                .build(),
                        ResourceMetric.ResourceRecord.builder()
                                .resources(Map.of("workerA", 21, "workerB", 25))
                                .totalWorkers(46.0)
                                .region("System")
                                .build()))
                .build());
        assertEquals(expectedMetrics, metrics, "Metrics list should match the expected metrics");
        // Further assertions can be added to verify the content of the metrics
    }

    // Helper method to create a mock EndStormEvent with a given stormId
    private EndStormEvent createMockEndStormEvent(String stormId) {
        EndStormEvent event = new EndStormEvent();
        StormOverview stormData = new StormOverview();
        stormData.setId(stormId);
        event.setStormData(stormData);
        return event;
    }

    private ETR mockETRWithEmptyResources(String stormId) {
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);

        ETR mockEtr = ETR.builder()
                .stormId(stormId)
                .sessionId(new ObjectId())
                .generationTime(now.minusHours(1))
                .etrType("result")
                .stormStartDate(now.minusDays(1))
                .systemResources(Map.of("workerA", 2, "workerB", 4))
                .regionalETRs(Arrays.asList(
                        ETR.RegionalETR.builder()
                                .location("Edison")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(5))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build(),
                        ETR.RegionalETR.builder()
                                .location("McKeesport")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(6))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build()))
                .resources(List.of())
                .build();

        return mockEtr;
    }

    private ETR mockETRWithNullResources(String stormId) {
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);

        ETR mockEtr = ETR.builder()
                .stormId(stormId)
                .sessionId(new ObjectId())
                .generationTime(now.minusHours(1))
                .etrType("result")
                .stormStartDate(now.minusDays(1))
                .systemResources(Map.of("workerA", 2, "workerB", 4))
                .regionalETRs(Arrays.asList(
                        ETR.RegionalETR.builder()
                                .location("Edison")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(5))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build(),
                        ETR.RegionalETR.builder()
                                .location("McKeesport")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(6))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build()))
                .resources(null)
                .build();

        return mockEtr;
    }

    private ETR mockETRWithSystemResources(String stormId) {
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);

        ETR mockEtr = ETR.builder()
                .stormId(stormId)
                .sessionId(new ObjectId())
                .generationTime(now.minusHours(1))
                .etrType("result")
                .stormStartDate(now.minusDays(1))
                .systemResources(Map.of("workerA", 2, "workerB", 4))
                .regionalETRs(Arrays.asList(
                        ETR.RegionalETR.builder()
                                .location("Edison")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(5))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build(),
                        ETR.RegionalETR.builder()
                                .location("McKeesport")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(6))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build()))
                .resources(List.of(ETR.Resources.builder()
                        .territoryId("System")
                        .territoryName("System")
                        .resources(Map.of("workerA", 10, "workerB", 12))
                        .build()))
                .build();

        return mockEtr;
    }

    private ETR mockETRWithOutSystemResources(String stormId) {
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);

        ETR mockEtr = ETR.builder()
                .stormId(stormId)
                .sessionId(new ObjectId())
                .generationTime(now.minusHours(1))
                .etrType("result")
                .stormStartDate(now.minusDays(1))
                .systemResources(Map.of("workerA", 2, "workerB", 4))
                .regionalETRs(Arrays.asList(
                        ETR.RegionalETR.builder()
                                .location("Edison")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(5))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build(),
                        ETR.RegionalETR.builder()
                                .location("McKeesport")
                                .outageScale("medium")
                                .projectedETR(now.plusHours(6))
                                .type(ETR.RegionalETR.Type.CURRENT)
                                .build()))
                .resources(List.of(
                        ETR.Resources.builder()
                                .territoryId("Edison")
                                .territoryName("Edison")
                                .resources(Map.of("workerA", 10, "workerB", 12))
                                .build(),
                        ETR.Resources.builder()
                                .territoryId("McKeesport")
                                .territoryName("McKeesport")
                                .resources(Map.of("workerA", 11, "workerB", 13))
                                .build()))
                .build();

        return mockEtr;
    }
}
