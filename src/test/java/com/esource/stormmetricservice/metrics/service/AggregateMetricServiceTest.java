package com.esource.stormmetricservice.metrics.service;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric;
import com.esource.stormmetricservice.aggregatemetrics.model.Overview;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Resource;
import com.esource.stormmetricservice.aggregatemetrics.repository.OverviewRepository;
import com.esource.stormmetricservice.aggregatemetrics.service.AggregateMetricService;

import static org.bson.assertions.Assertions.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AggregateMetricServiceTest {
    @Mock
    private OverviewRepository overviewRepository;

    @InjectMocks
    private AggregateMetricService aggregateMetricService;

    @Test
    void testGetAggregateMetricsWithStormWindow() {
        ZonedDateTime start = ZonedDateTime.parse("2021-01-01T12:00:00Z");
        ZonedDateTime end = ZonedDateTime.parse("2021-01-01T18:00:00Z");
        ZonedDateTime stormStart = ZonedDateTime.parse("2021-01-01T13:00:00Z");
        ZonedDateTime stormEnd = ZonedDateTime.parse("2021-01-01T16:00:00Z");

        List<Overview> overviews = List.of(
                createOverview(start, 10, 100, 10, 100),
                createOverview(start.plusHours(1), 15, 150, 5, 50),
                createOverview(start.plusHours(2), 20, 200, 5, 50),
                createOverview(start.plusHours(3), 0, 0, 0, 0),
                createOverview(start.plusHours(4), 30, 300, 5, 50),
                createOverview(start.plusHours(5), 10, 100, 5, 75));
        ZonedDateTime startDate = start.minusSeconds(1);
        ZonedDateTime endDate = end.plusSeconds(1);
        when(overviewRepository.findByTimestampBetweenWithWeather(
                        startDate,
                        endDate,
                        startDate.truncatedTo(ChronoUnit.HOURS),
                        endDate.truncatedTo(ChronoUnit.HOURS)))
                .thenReturn(overviews);

        Map<String, Integer> expectedWindGustThresholds = Map.of("over20mph", 0, "over30mph", 0, "over40mph", 0);

        AggregateMetric expected = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .totalIncidents(10)
                                        .totalAffectedCustomers(100)
                                        .uniqueIncidentsCount(10)
                                        .uniqueAffectedCustomers(100)
                                        .totalResources(0)
                                        .totalRestorations(0)
                                        .totalAffectedCustomerRestorations(0)
                                        .cumulativeAffectedCustomers(0)
                                        .cumulativeRestorations(0)
                                        .cumulativeIncidents(0)
                                        .cumulativeCustomersRestored(0)
                                        .restoredPerWorker(0.0)
                                        .windGustThresholds(expectedWindGustThresholds)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .totalIncidents(15)
                                        .totalAffectedCustomers(150)
                                        .uniqueIncidentsCount(5)
                                        .uniqueAffectedCustomers(50)
                                        .totalResources(0)
                                        .totalRestorations(0)
                                        .totalAffectedCustomerRestorations(0)
                                        .cumulativeAffectedCustomers(150)
                                        .cumulativeRestorations(0)
                                        .cumulativeIncidents(15)
                                        .cumulativeCustomersRestored(0)
                                        .restoredPerWorker(0.0)
                                        .windGustThresholds(expectedWindGustThresholds)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(2))
                                        .totalIncidents(20)
                                        .totalAffectedCustomers(200)
                                        .uniqueIncidentsCount(5)
                                        .uniqueAffectedCustomers(50)
                                        .totalResources(0)
                                        .totalRestorations(0)
                                        .totalAffectedCustomerRestorations(0)
                                        .cumulativeAffectedCustomers(200)
                                        .cumulativeRestorations(0)
                                        .cumulativeIncidents(20)
                                        .cumulativeCustomersRestored(0)
                                        .restoredPerWorker(0.0)
                                        .windGustThresholds(expectedWindGustThresholds)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(3))
                                        .totalIncidents(0)
                                        .totalAffectedCustomers(0)
                                        .uniqueIncidentsCount(0)
                                        .uniqueAffectedCustomers(0)
                                        .totalResources(0)
                                        .totalRestorations(20)
                                        .totalAffectedCustomerRestorations(200)
                                        .cumulativeAffectedCustomers(200)
                                        .cumulativeRestorations(20)
                                        .cumulativeIncidents(20)
                                        .cumulativeCustomersRestored(200)
                                        .restoredPerWorker(0.0)
                                        .windGustThresholds(expectedWindGustThresholds)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(4))
                                        .totalIncidents(30)
                                        .totalAffectedCustomers(300)
                                        .uniqueIncidentsCount(5)
                                        .uniqueAffectedCustomers(50)
                                        .totalResources(0)
                                        .totalRestorations(0)
                                        .totalAffectedCustomerRestorations(0)
                                        .cumulativeAffectedCustomers(250)
                                        .cumulativeRestorations(20)
                                        .cumulativeIncidents(25)
                                        .cumulativeCustomersRestored(200)
                                        .restoredPerWorker(0.0)
                                        .windGustThresholds(expectedWindGustThresholds)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(5))
                                        .totalIncidents(10)
                                        .totalAffectedCustomers(100)
                                        .uniqueIncidentsCount(5)
                                        .uniqueAffectedCustomers(75)
                                        .totalResources(0)
                                        .totalRestorations(20)
                                        .totalAffectedCustomerRestorations(200)
                                        .cumulativeAffectedCustomers(250)
                                        .cumulativeRestorations(20)
                                        .cumulativeIncidents(25)
                                        .cumulativeCustomersRestored(200)
                                        .restoredPerWorker(0.0)
                                        .windGustThresholds(expectedWindGustThresholds)
                                        .build())))
                .build();

        AggregateMetric actual = aggregateMetricService.getAggregateMetrics(start, end, stormStart, stormEnd);

        assertEquals(expected, actual);
    }

    @Test
    void testGetAggregateMetricsWithStormWindowWithRegionFilters() {
        ZonedDateTime start = ZonedDateTime.parse("2021-01-01T12:00:00Z");
        ZonedDateTime end = ZonedDateTime.parse("2021-01-01T18:00:00Z");

        AggregateMetric input = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .totalIncidents(10)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .totalIncidents(15)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(2))
                                        .totalIncidents(20)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(3))
                                        .totalIncidents(0)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(4))
                                        .totalIncidents(30)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(5))
                                        .totalIncidents(10)
                                        .build()),
                        "Region2",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .totalIncidents(10)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .totalIncidents(15)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(2))
                                        .totalIncidents(20)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(3))
                                        .totalIncidents(0)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(4))
                                        .totalIncidents(30)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(5))
                                        .totalIncidents(10)
                                        .build())))
                .build();

        AggregateMetric expected = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .totalIncidents(10)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .totalIncidents(15)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(2))
                                        .totalIncidents(20)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(3))
                                        .totalIncidents(0)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(4))
                                        .totalIncidents(30)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(5))
                                        .totalIncidents(10)
                                        .build())))
                .build();

        AggregateMetric actual = aggregateMetricService.filterAggregateMetrics(input, List.of("Region1"));

        assertEquals(expected, actual);
    }

    @Test
    void testGetAggregateMetricsWithStormWindowWithRegionFiltersEmpty() {
        ZonedDateTime start = ZonedDateTime.parse("2021-01-01T12:00:00Z");
        ZonedDateTime end = ZonedDateTime.parse("2021-01-01T18:00:00Z");

        AggregateMetric expected = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .totalIncidents(10)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .totalIncidents(15)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(2))
                                        .totalIncidents(20)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(3))
                                        .totalIncidents(0)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(4))
                                        .totalIncidents(30)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(5))
                                        .totalIncidents(10)
                                        .build())))
                .build();

        AggregateMetric actual = aggregateMetricService.filterAggregateMetrics(expected, List.of());

        assertEquals(expected, actual);
    }

    @Test
    void testGetAggregateMetricsWithStormWindowWithRegionFiltersNull() {
        ZonedDateTime start = ZonedDateTime.parse("2021-01-01T12:00:00Z");
        ZonedDateTime end = ZonedDateTime.parse("2021-01-01T18:00:00Z");

        AggregateMetric expected = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .totalIncidents(10)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .totalIncidents(15)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(2))
                                        .totalIncidents(20)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(3))
                                        .totalIncidents(0)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(4))
                                        .totalIncidents(30)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(5))
                                        .totalIncidents(10)
                                        .build())))
                .build();

        AggregateMetric actual = aggregateMetricService.filterAggregateMetrics(expected, null);

        assertEquals(expected, actual);
    }

    @Test
    void testGetAggregateMetricsWithStormWindowWithRegionFiltersNotPresentInList() {
        ZonedDateTime start = ZonedDateTime.parse("2021-01-01T12:00:00Z");
        ZonedDateTime end = ZonedDateTime.parse("2021-01-01T18:00:00Z");

        AggregateMetric input = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .totalIncidents(10)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .totalIncidents(15)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(2))
                                        .totalIncidents(20)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(3))
                                        .totalIncidents(0)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(4))
                                        .totalIncidents(30)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(5))
                                        .totalIncidents(10)
                                        .build())))
                .build();

        AggregateMetric expected = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of())
                .build();

        AggregateMetric actual = aggregateMetricService.filterAggregateMetrics(input, List.of("Region2"));

        assertEquals(expected, actual);
    }

    @Test
    void testAggregateMetricsByGranularityWithTwoMetricsAgainstLastGroupedTimestamp() {
        List<AggregateMetric.OverviewMetrics> metrics = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:00:00Z"), 10, 100),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:01:00Z"), 20, 200),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:02:00Z"), 30, 300),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:03:00Z"), 40, 400));

        AggregateMetric filteredMetrics = AggregateMetric.builder()
                .regionalMetrics(Map.of("Region1", metrics))
                .startTime(ZonedDateTime.parse("2021-01-01T12:00:00Z"))
                .endTime(ZonedDateTime.parse("2021-01-01T12:03:00Z"))
                .build();

        AggregateMetric result = aggregateMetricService.aggregateMetricsByGranularity(filteredMetrics, 2);

        List<AggregateMetric.OverviewMetrics> expected = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:00:00Z"), 10, 100),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:02:00Z"), 30, 300),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:03:00Z"), 40, 400));

        assertEquals(expected, result.getRegionalMetrics().get("Region1"));
        assertEquals(ZonedDateTime.parse("2021-01-01T12:00:00Z"), result.getStartTime());
        assertEquals(ZonedDateTime.parse("2021-01-01T12:03:00Z"), result.getEndTime());
    }

    @Test
    void testAggregateMetricsByGranularityWithOneMetricAgainstLastGroupedTimestamp() {
        List<AggregateMetric.OverviewMetrics> metrics = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:00:00Z"), 10, 100),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:01:00Z"), 20, 200),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:02:00Z"), 30, 300));

        AggregateMetric filteredMetrics = AggregateMetric.builder()
                .regionalMetrics(Map.of("Region1", metrics))
                .startTime(ZonedDateTime.parse("2021-01-01T12:00:00Z"))
                .endTime(ZonedDateTime.parse("2021-01-01T12:02:00Z"))
                .build();

        AggregateMetric result = aggregateMetricService.aggregateMetricsByGranularity(filteredMetrics, 2);

        List<AggregateMetric.OverviewMetrics> expected = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:00:00Z"), 10, 100),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:02:00Z"), 30, 300));

        assertEquals(expected, result.getRegionalMetrics().get("Region1"));
        assertEquals(ZonedDateTime.parse("2021-01-01T12:00:00Z"), result.getStartTime());
        assertEquals(ZonedDateTime.parse("2021-01-01T12:02:00Z"), result.getEndTime());
    }

    @Test
    void testAggregateMetricsByGranularityWithEmptyMetrics() {
        AggregateMetric filteredMetrics = AggregateMetric.builder()
                .regionalMetrics(Map.of("Region1", List.of()))
                .startTime(ZonedDateTime.parse("2021-01-01T12:00:00Z"))
                .endTime(ZonedDateTime.parse("2021-01-01T12:03:00Z"))
                .build();

        AggregateMetric result = aggregateMetricService.aggregateMetricsByGranularity(filteredMetrics, 2);

        Assertions.assertTrue(result.getRegionalMetrics().get("Region1").isEmpty());
        assertEquals(ZonedDateTime.parse("2021-01-01T12:00:00Z"), result.getStartTime());
        assertEquals(ZonedDateTime.parse("2021-01-01T12:03:00Z"), result.getEndTime());
    }

    @Test
    void testAggregateMetricsByGranularityWithNullMetrics() {
        AggregateMetric filteredMetrics = AggregateMetric.builder()
                .regionalMetrics(new HashMap<>())
                .startTime(ZonedDateTime.parse("2021-01-01T12:00:00Z"))
                .endTime(ZonedDateTime.parse("2021-01-01T12:03:00Z"))
                .build();
        filteredMetrics.getRegionalMetrics().put("Region1", null);

        AggregateMetric result = aggregateMetricService.aggregateMetricsByGranularity(filteredMetrics, 2);

        // Check that the result has an empty list for "Region1"
        assertNotNull(result.getRegionalMetrics().get("Region1"));
        assertTrue(result.getRegionalMetrics().get("Region1").isEmpty());
        assertEquals(ZonedDateTime.parse("2021-01-01T12:00:00Z"), result.getStartTime());
        assertEquals(ZonedDateTime.parse("2021-01-01T12:03:00Z"), result.getEndTime());
    }

    @Test
    void testAggregateMetricsByGranularityWithSingleMetric() {
        List<AggregateMetric.OverviewMetrics> metrics =
                List.of(createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:00:00Z"), 10, 100));

        AggregateMetric filteredMetrics = AggregateMetric.builder()
                .regionalMetrics(Map.of("Region1", metrics))
                .startTime(ZonedDateTime.parse("2021-01-01T12:00:00Z"))
                .endTime(ZonedDateTime.parse("2021-01-01T12:03:00Z"))
                .build();

        AggregateMetric result = aggregateMetricService.aggregateMetricsByGranularity(filteredMetrics, 2);

        assertEquals(metrics, result.getRegionalMetrics().get("Region1"));
        assertEquals(ZonedDateTime.parse("2021-01-01T12:00:00Z"), result.getStartTime());
        assertEquals(ZonedDateTime.parse("2021-01-01T12:03:00Z"), result.getEndTime());
    }

    @Test
    void testAggregateMetricsByGranularityWithMultipleRegions() {
        List<AggregateMetric.OverviewMetrics> metricsRegion1 = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:00:00Z"), 10, 100),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:01:00Z"), 20, 200));

        List<AggregateMetric.OverviewMetrics> metricsRegion2 = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T13:00:00Z"), 30, 300),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T13:01:00Z"), 40, 400));

        AggregateMetric filteredMetrics = AggregateMetric.builder()
                .regionalMetrics(Map.of(
                        "Region1", metricsRegion1,
                        "Region2", metricsRegion2))
                .startTime(ZonedDateTime.parse("2021-01-01T12:00:00Z"))
                .endTime(ZonedDateTime.parse("2021-01-01T13:03:00Z"))
                .build();

        AggregateMetric result = aggregateMetricService.aggregateMetricsByGranularity(filteredMetrics, 2);

        List<AggregateMetric.OverviewMetrics> expectedRegion1 = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:00:00Z"), 10, 100),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:01:00Z"), 20, 200));

        List<AggregateMetric.OverviewMetrics> expectedRegion2 = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T13:00:00Z"), 30, 300),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T13:01:00Z"), 40, 400));

        assertEquals(expectedRegion1, result.getRegionalMetrics().get("Region1"));
        assertEquals(expectedRegion2, result.getRegionalMetrics().get("Region2"));
        assertEquals(ZonedDateTime.parse("2021-01-01T12:00:00Z"), result.getStartTime());
        assertEquals(ZonedDateTime.parse("2021-01-01T13:03:00Z"), result.getEndTime());
    }

    @Test
    void testAggregateMetricsByGranularityWithDifferentGranularities() {
        List<AggregateMetric.OverviewMetrics> metrics = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:00:00Z"), 10, 100),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:01:00Z"), 20, 200),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:02:00Z"), 30, 300),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:03:00Z"), 40, 400));

        AggregateMetric filteredMetrics = AggregateMetric.builder()
                .regionalMetrics(Map.of("Region1", metrics))
                .startTime(ZonedDateTime.parse("2021-01-01T12:00:00Z"))
                .endTime(ZonedDateTime.parse("2021-01-01T12:03:00Z"))
                .build();

        AggregateMetric result = aggregateMetricService.aggregateMetricsByGranularity(filteredMetrics, 3);

        List<AggregateMetric.OverviewMetrics> expected = List.of(
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:00:00Z"), 10, 100),
                createOverviewMetrics(ZonedDateTime.parse("2021-01-01T12:03:00Z"), 40, 400));

        assertEquals(expected, result.getRegionalMetrics().get("Region1"));
        assertEquals(ZonedDateTime.parse("2021-01-01T12:00:00Z"), result.getStartTime());
        assertEquals(ZonedDateTime.parse("2021-01-01T12:03:00Z"), result.getEndTime());
    }

    private AggregateMetric.OverviewMetrics createOverviewMetrics(
            ZonedDateTime timestamp, int incidents, int affectedCustomers) {
        return AggregateMetric.OverviewMetrics.builder()
                .timestamp(timestamp)
                .totalIncidents(incidents)
                .totalAffectedCustomers(affectedCustomers)
                .build();
    }

    private Overview createOverview(
            ZonedDateTime timestamp,
            int totalIncidents,
            int totalAffectedCustomers,
            int uniqueIncidentsCount,
            int uniqueAffectedCustomers) {
        Map<String, Object> weatherStationMap = new HashMap<>();
        weatherStationMap.put("stationId", UUID.randomUUID());
        weatherStationMap.put("latitude", 33.6598);
        weatherStationMap.put("longitude", -85.8314);
        weatherStationMap.put("region", "Region1");
        weatherStationMap.put(
                "weatherAttributes",
                Map.of(
                        "temperatureCelsius", 27.0,
                        "humidityPercent", 65.0,
                        "windBearingDeg", 190,
                        "windGustmph", 35.0,
                        "windSpeedmph", 20.0));

        List<Object> stationData = List.of(weatherStationMap);

        return Overview.builder()
                .timestamp(timestamp)
                .data(Overview.OverviewData.builder()
                        .outages(Map.of(
                                "Region1",
                                Outage.builder()
                                        .totalIncidents(totalIncidents)
                                        .totalAffectedCustomers(totalAffectedCustomers)
                                        .uniqueIncidentsCount(uniqueIncidentsCount)
                                        .uniqueAffectedCustomers(uniqueAffectedCustomers)
                                        .build()))
                        .resources(Map.of(
                                "Region1", Resource.builder().totalResources(0).build()))
                        .stationData(stationData)
                        .build())
                .build();
    }
}
