package com.esource.stormmetricservice.metrics.service.aggregators;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.esource.stormmetricservice.aggregatemetrics.model.dto.Resource;
import com.esource.stormmetricservice.aggregatemetrics.service.aggregators.ResourceAggregator;
import com.esource.stormmetricservice.etr.model.ResourceEntity;
import com.esource.stormmetricservice.etr.model.StormRecord;
import com.esource.stormmetricservice.etr.model.dto.AggregatedResourcesDTO;
import com.esource.stormmetricservice.etr.repository.StormRecordRepository;
import com.esource.stormmetricservice.etr.service.ResourceService;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEvent;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ResourceAggregatorTest {

    @Mock
    private ResourceService resourceService;

    @Mock
    private StormRecordRepository stormRecordRepository;

    @InjectMocks
    private ResourceAggregator resourceAggregator;

    @Test
    void testResourceAggregator() {
        List<AggregatedResourcesDTO> resourcesDTOS = List.of(
                AggregatedResourcesDTO.builder()
                        .territoryName("Anniston")
                        .totalResources(Map.of("contractWorkers", 10, "foreignWorkers", 20))
                        .build(),
                AggregatedResourcesDTO.builder()
                        .territoryName("Edison")
                        .totalResources(Map.of("contractWorkers", 20, "foreignWorkers", 30))
                        .build());

        ZonedDateTime currentTime = ZonedDateTime.parse("2023-09-28T15:19:56.760Z");

        when(stormRecordRepository.findActiveNonArchivedStorms(Mockito.any(ZonedDateTime.class)))
                .thenReturn(List.of(StormRecord.builder().id("stormId").build()));

        when(resourceService.getCurrentResources(
                        "stormId", currentTime, ResourceEntity.ResourceConstants.GENERATION_TYPE_MANUAL, null))
                .thenReturn(resourcesDTOS);

        Map<String, Resource> aggregatedResources = resourceAggregator.aggregateResources(
                AggregateOverviewEvent.builder().timestamp(currentTime).build());

        Map<String, Resource> expectedResources = Map.of(
                "Anniston",
                Resource.builder()
                        .totalResources(30)
                        .resources(Map.of("contractWorkers", 10, "foreignWorkers", 20))
                        .build(),
                "Edison",
                Resource.builder()
                        .totalResources(50)
                        .resources(Map.of("contractWorkers", 20, "foreignWorkers", 30))
                        .build(),
                "System",
                Resource.builder()
                        .totalResources(80)
                        .resources(Map.of("contractWorkers", 30, "foreignWorkers", 50))
                        .build());
        assertEquals(expectedResources, aggregatedResources);
    }
}
