package com.esource.stormmetricservice.metrics.service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric;
import com.esource.stormmetricservice.aggregatemetrics.model.AggregateStormMetric;
import com.esource.stormmetricservice.aggregatemetrics.repository.AggregateStormMetricRepository;
import com.esource.stormmetricservice.aggregatemetrics.service.AggregateMetricService;
import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.metrics.model.StormRetrospective;
import com.esource.stormmetricservice.metrics.model.metrics.CumulativeOutageMetric;
import com.esource.stormmetricservice.metrics.repository.StormRetrospectiveRepository;
import com.esource.stormmetricservice.metrics.service.summarizer.StormSummarizerServiceV2;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class StormSummarizerServiceV2Tests {

    @Mock
    private StormRetrospectiveRepository stormRetrospectiveRepository;

    @Mock
    private MetricService metricService;

    @Mock
    private AggregateMetricService aggregateMetricService;

    @Mock
    private AggregateStormMetricRepository aggregateStormMetricRepository;

    @InjectMocks
    private StormSummarizerServiceV2 stormSummarizerService;

    private EndStormEvent endStormEvent;
    private AggregateStormMetric aggregateStormMetric;
    private AggregateMetric aggregateMetric;
    private StormMetric cumulativeOutageMetric;

    private ZonedDateTime testTime;

    @BeforeEach
    void setUp() {
        testTime = ZonedDateTime.now();

        StormOverview stormOverview = StormOverview.builder()
                .id("6466b4577ec2164a25e964ae")
                .startDate(testTime.minusDays(1))
                .endDate(testTime)
                .build();

        endStormEvent = EndStormEvent.builder()
                .eventName("stormEndEvent")
                .timestamp(testTime)
                .stormData(stormOverview)
                .build();

        aggregateMetric = AggregateMetric.builder()
                .startTime(testTime.minusDays(1))
                .endTime(testTime)
                .regionalMetrics(Map.of("Region1", createOverviewMetricsList()))
                .build();

        aggregateStormMetric = AggregateStormMetric.builder()
                .id("6466b4577ec2164a25e964ab")
                .stormOverview(stormOverview)
                .aggregateMetric(aggregateMetric)
                .build();

        cumulativeOutageMetric = StormMetric.builder()
                .stormId(new org.bson.types.ObjectId())
                .metricType(MetricConstants.METRIC_TYPE_CUMULATIVE_OUTAGES)
                .metrics(List.of(createCumulativeOutageMetric()))
                .lastUpdated(testTime)
                .stormData(stormOverview)
                .build();
    }

    @Test
    void testPreAggregate_ExistingAggregateStormMetric() {
        when(aggregateStormMetricRepository.findByStormOverviewId(anyString()))
                .thenReturn(Optional.of(aggregateStormMetric));

        AggregateStormMetric result = stormSummarizerService.preAggregate(endStormEvent);

        assertEquals(aggregateStormMetric, result);
        verify(aggregateStormMetricRepository).findByStormOverviewId("6466b4577ec2164a25e964ae");
        verify(aggregateMetricService, never()).getAggregateMetrics(any(), any(), any(), any());
    }

    @Test
    void testPreAggregate_NewAggregateStormMetric() {
        when(aggregateStormMetricRepository.findByStormOverviewId(anyString())).thenReturn(Optional.empty());
        when(aggregateMetricService.getAggregateMetrics(any(), any(), any(), any()))
                .thenReturn(aggregateMetric);
        when(aggregateStormMetricRepository.save(any())).thenReturn(aggregateStormMetric);

        AggregateStormMetric result = stormSummarizerService.preAggregate(endStormEvent);

        assertEquals(aggregateStormMetric, result);
        verify(aggregateStormMetricRepository).findByStormOverviewId("6466b4577ec2164a25e964ae");
        verify(aggregateMetricService)
                .getAggregateMetrics(
                        endStormEvent.getStormData().getStartDate(),
                        endStormEvent.getStormData().getEndDate(),
                        null,
                        null);
        verify(aggregateStormMetricRepository).save(any());
    }

    @Test
    void testSummarize_ExistingRetrospective() {
        StormRetrospective existingRetrospective = StormRetrospective.builder()
                .id("6466b4577ec2164a25e964aa")
                .stormId("6466b4577ec2164a25e964ae")
                .build();

        when(stormRetrospectiveRepository.findByStormId(anyString())).thenReturn(Optional.of(existingRetrospective));

        StormRetrospective result = stormSummarizerService.summarize(endStormEvent, aggregateStormMetric);

        assertEquals(existingRetrospective, result);
        verify(stormRetrospectiveRepository).findByStormId("6466b4577ec2164a25e964ae");
        verify(metricService, never()).getMetricByStormIdAndType(anyString(), anyString(), anyBoolean());
    }

    @Test
    void testSummarize_NewRetrospective() {
        when(stormRetrospectiveRepository.findByStormId(anyString())).thenReturn(Optional.empty());
        when(metricService.getMetricByStormIdAndType(anyString(), anyString(), anyBoolean()))
                .thenReturn(Optional.of(cumulativeOutageMetric));
        when(stormRetrospectiveRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

        StormRetrospective result = stormSummarizerService.summarize(endStormEvent, aggregateStormMetric);

        assertNotNull(result);
        assertEquals("6466b4577ec2164a25e964ae", result.getStormId());
        assertNotNull(result.getStormEndSummary());
        assertEquals(1, result.getStormEndSummary().size());
        assertTrue(result.getStormEndSummary().containsKey("Region1"));

        StormRetrospective.StormEndSummary summary = result.getStormEndSummary().get("Region1");
        assertEquals(100.0, summary.getTotalCumulativeIncidents());
        assertEquals(1000.0, summary.getTotalCumulativeCustomersAffected());
        assertEquals(15.0, summary.getAverageWindSpeed());
        assertNotNull(summary.getWindGustThresholds());
        assertEquals(3, summary.getWindGustThresholds().size());

        verify(stormRetrospectiveRepository).findByStormId("6466b4577ec2164a25e964ae");
        verify(metricService)
                .getMetricByStormIdAndType(
                        "6466b4577ec2164a25e964ae", MetricConstants.METRIC_TYPE_CUMULATIVE_OUTAGES, false);
        verify(stormRetrospectiveRepository).save(any());
    }

    private List<AggregateMetric.OverviewMetrics> createOverviewMetricsList() {
        return List.of(AggregateMetric.OverviewMetrics.builder()
                .timestamp(testTime)
                .totalIncidents(50)
                .totalAffectedCustomers(500)
                .averageWindSpeed(15.0)
                .windGustThresholds(Map.of("over20mph", 100, "over30mph", 50, "over40mph", 25))
                .build());
    }

    private CumulativeOutageMetric createCumulativeOutageMetric() {
        CumulativeOutageMetric.CumulativeOutageRecord record = CumulativeOutageMetric.CumulativeOutageRecord.builder()
                .region("Region1")
                .cumulativeIncidents(100)
                .cumulativeCustomerOutages(1000)
                .build();

        return CumulativeOutageMetric.builder()
                .records(List.of(record))
                .timestamp(testTime)
                .build();
    }
}
