package com.esource.stormmetricservice.metrics.service.aggregators;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.aggregatemetrics.service.aggregators.UnaggregatedOutageAggregator;
import com.esource.stormmetricservice.etr.model.UnaggregatedOutage;
import com.esource.stormmetricservice.etr.repository.UnaggregatedOutageRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEvent;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UnaggregatedOutageAggregatorTests {

    @Mock
    private UnaggregatedOutageRepository unaggregatedOutageRepository;

    private UnaggregatedOutageAggregator unaggregatedOutageAggregator;

    @BeforeEach
    public void setUp() {
        String[] regions = {"Region1", "Region2", "Region3"};
        unaggregatedOutageAggregator = new UnaggregatedOutageAggregator(unaggregatedOutageRepository, regions, 2);
    }

    @Test
    void testAggregateOutages_WithPreviousOutage() {
        // Arrange
        ZonedDateTime referenceTime = ZonedDateTime.now();
        AggregateOverviewEvent event = new AggregateOverviewEvent();
        event.setTimestamp(referenceTime);
        event.setMetadata(Map.of("unaggregatedOutageId", "507f1f77bcf86cd799439010"));

        UnaggregatedOutage currentOutage = createUnaggregatedOutage(
                "507f1f77bcf86cd799439010",
                referenceTime,
                Arrays.asList(
                        createRawOutage("Region1", "Incident1", 100),
                        createRawOutage("Region1", "Incident2", 50),
                        createRawOutage("Region2", "Incident3", 200)));

        UnaggregatedOutage previousOutage = createUnaggregatedOutage(
                "507f1f77bcf86cd799439011",
                referenceTime.minusMinutes(5),
                Arrays.asList(
                        createRawOutage("Region1", "Incident1", 100), createRawOutage("Region2", "Incident3", 200)));

        when(unaggregatedOutageRepository.findFirstByCreatedTimestampLessThanEqualOrderByCreatedTimestampDesc(
                        any(ZonedDateTime.class)))
                .thenReturn(Optional.of(currentOutage));
        when(unaggregatedOutageRepository.findFirstByCreatedTimestampLessThanOrderByCreatedTimestampDesc(
                        any(ZonedDateTime.class)))
                .thenReturn(Optional.of(previousOutage));

        // Act
        Map<String, Outage> result = unaggregatedOutageAggregator.aggregateOutages(event);

        // Assert
        assertEquals(4, result.size());

        Outage region1Outage = result.get("Region1");
        assertEquals(2, region1Outage.getTotalIncidents());
        assertEquals(150, region1Outage.getTotalAffectedCustomers());
        assertEquals(1, region1Outage.getUniqueIncidentsCount());
        assertEquals(50, region1Outage.getUniqueAffectedCustomers());

        Outage region2Outage = result.get("Region2");
        assertEquals(1, region2Outage.getTotalIncidents());
        assertEquals(200, region2Outage.getTotalAffectedCustomers());
        assertEquals(0, region2Outage.getUniqueIncidentsCount());
        assertEquals(0, region2Outage.getUniqueAffectedCustomers());

        Outage region3Outage = result.get("Region3");
        assertEquals(0, region3Outage.getTotalIncidents());
        assertEquals(0, region3Outage.getTotalAffectedCustomers());
        assertEquals(0, region3Outage.getUniqueIncidentsCount());
        assertEquals(0, region3Outage.getUniqueAffectedCustomers());

        Outage systemOutage = result.get("System");
        assertEquals(3, systemOutage.getTotalIncidents());
        assertEquals(350, systemOutage.getTotalAffectedCustomers());
        assertEquals(1, systemOutage.getUniqueIncidentsCount());
        assertEquals(50, systemOutage.getUniqueAffectedCustomers());
    }

    @Test
    void testAggregateOutages_WithoutPreviousOutage() {
        // Arrange
        ZonedDateTime referenceTime = ZonedDateTime.now();
        AggregateOverviewEvent event = new AggregateOverviewEvent();
        event.setTimestamp(referenceTime);
        event.setMetadata(Map.of("unaggregatedOutageId", "507f1f77bcf86cd799439010"));

        UnaggregatedOutage currentOutage = createUnaggregatedOutage(
                "507f1f77bcf86cd799439010",
                referenceTime,
                Arrays.asList(
                        createRawOutage("Region1", "Incident1", 100),
                        createRawOutage("Region1", "Incident2", 50),
                        createRawOutage("Region2", "Incident3", 200)));

        when(unaggregatedOutageRepository.findFirstByCreatedTimestampLessThanEqualOrderByCreatedTimestampDesc(
                        any(ZonedDateTime.class)))
                .thenReturn(Optional.of(currentOutage));
        when(unaggregatedOutageRepository.findFirstByCreatedTimestampLessThanOrderByCreatedTimestampDesc(
                        any(ZonedDateTime.class)))
                .thenReturn(Optional.empty());

        // Act
        Map<String, Outage> result = unaggregatedOutageAggregator.aggregateOutages(event);

        // Assert
        assertEquals(4, result.size());

        Outage region1Outage = result.get("Region1");
        assertEquals(2, region1Outage.getTotalIncidents());
        assertEquals(150, region1Outage.getTotalAffectedCustomers());
        assertEquals(2, region1Outage.getUniqueIncidentsCount());
        assertEquals(150, region1Outage.getUniqueAffectedCustomers());

        Outage region2Outage = result.get("Region2");
        assertEquals(1, region2Outage.getTotalIncidents());
        assertEquals(200, region2Outage.getTotalAffectedCustomers());
        assertEquals(1, region2Outage.getUniqueIncidentsCount());
        assertEquals(200, region2Outage.getUniqueAffectedCustomers());

        Outage systemOutage = result.get("System");
        assertEquals(3, systemOutage.getTotalIncidents());
        assertEquals(350, systemOutage.getTotalAffectedCustomers());
        assertEquals(3, systemOutage.getUniqueIncidentsCount());
        assertEquals(350, systemOutage.getUniqueAffectedCustomers());
    }

    private UnaggregatedOutage createUnaggregatedOutage(
            String id, ZonedDateTime timestamp, List<UnaggregatedOutage.RawOutage> rawOutages) {
        return UnaggregatedOutage.builder()
                .id(new ObjectId(id))
                .createdTimestamp(timestamp)
                .rawOutages(rawOutages)
                .build();
    }

    private UnaggregatedOutage.RawOutage createRawOutage(String region, String incidentName, int affectedCustomers) {
        return UnaggregatedOutage.RawOutage.builder()
                .region(region)
                .incidentName(incidentName)
                .affectedCustomers(affectedCustomers)
                .build();
    }
}
