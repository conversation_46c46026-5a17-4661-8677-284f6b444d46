package com.esource.stormmetricservice.metrics.service;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.esource.AbstractIntegrationTestContainer;
import com.esource.stormmetricservice.etr.model.StormRecord;
import com.esource.stormmetricservice.etr.repository.StormRecordRepository;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.metrics.model.StormRetrospective;
import com.esource.stormmetricservice.metrics.repository.StormRetrospectiveRepository;
import com.esource.stormmetricservice.metrics.service.similarity.StormSimilarityService;

import static org.assertj.core.api.Assertions.assertThat;

public class StormSimilarityServiceIntegrationTest extends AbstractIntegrationTestContainer {

    @Autowired
    private StormSimilarityService testable;

    @Autowired
    private StormRetrospectiveRepository stormRetrospectiveRepository;

    @Autowired
    private StormRecordRepository stormRecordRepository;

    @AfterEach
    public void clean() {
        stormRecordRepository.deleteAll();
        stormRetrospectiveRepository.deleteAll();
    }

    @Test
    void testCalculateStormSimilarities() {

        StormOverview overview1 = StormOverview.builder()
                .id("1")
                .startDate(ZonedDateTime.now().minusDays(5))
                .endDate(ZonedDateTime.now().minusDays(3))
                .name("Storm 1")
                .build();
        StormOverview overview2 = StormOverview.builder()
                .id("2")
                .startDate(ZonedDateTime.now().minusDays(4))
                .endDate(ZonedDateTime.now().minusDays(2))
                .name("Storm 2")
                .build();
        StormOverview overview3 = StormOverview.builder()
                .id("3")
                .startDate(ZonedDateTime.now().minusDays(4))
                .endDate(ZonedDateTime.now().minusDays(2))
                .name("Storm 3")
                .build();

        Map<String, StormRetrospective.StormEndSummary> summary1 = new HashMap<>();
        summary1.put(
                "System",
                StormRetrospective.StormEndSummary.builder()
                        .totalCumulativeIncidents(100.0)
                        .totalCumulativeCustomersAffected(200.0)
                        .windGustThresholds(Map.of(
                                StormRetrospective.WIND_GUST_OVER_20,
                                10,
                                StormRetrospective.WIND_GUST_OVER_30,
                                5,
                                StormRetrospective.WIND_GUST_OVER_40,
                                2))
                        .averageWindSpeed(20.0)
                        .build());

        Map<String, StormRetrospective.StormEndSummary> summary2 = new HashMap<>();
        summary2.put(
                "System",
                StormRetrospective.StormEndSummary.builder()
                        .totalCumulativeIncidents(110.0)
                        .totalCumulativeCustomersAffected(190.0)
                        .windGustThresholds(Map.of(
                                StormRetrospective.WIND_GUST_OVER_20,
                                10,
                                StormRetrospective.WIND_GUST_OVER_30,
                                5,
                                StormRetrospective.WIND_GUST_OVER_40,
                                2))
                        .averageWindSpeed(18.0)
                        .build());
        Map<String, StormRetrospective.StormEndSummary> summary3 = new HashMap<>();
        summary3.put(
                "System",
                StormRetrospective.StormEndSummary.builder()
                        .totalCumulativeIncidents(105.0)
                        .totalCumulativeCustomersAffected(195.0)
                        .windGustThresholds(Map.of(
                                StormRetrospective.WIND_GUST_OVER_20,
                                10,
                                StormRetrospective.WIND_GUST_OVER_30,
                                5,
                                StormRetrospective.WIND_GUST_OVER_40,
                                2))
                        .averageWindSpeed(19.0)
                        .build());

        StormRetrospective storm1 = StormRetrospective.builder()
                .id("1")
                .stormId("1")
                .stormOverview(overview1)
                .stormEndSummary(summary1)
                .build();
        StormRetrospective storm2 = StormRetrospective.builder()
                .id("2")
                .stormId("2")
                .stormOverview(overview2)
                .stormEndSummary(summary2)
                .build();
        StormRetrospective storm3 = StormRetrospective.builder()
                .id("3")
                .stormId("3")
                .stormOverview(overview3)
                .stormEndSummary(summary3)
                .build();

        StormRecord stormRecord1 = StormRecord.builder()
                .id(storm1.getStormId())
                .stormName("storm1")
                .stormMode("archive")
                .creationDate(ZonedDateTime.now())
                .build();
        StormRecord stormRecord2 = StormRecord.builder()
                .id(storm2.getStormId())
                .stormName("storm2")
                .stormMode("archive")
                .creationDate(ZonedDateTime.now())
                .build();
        StormRecord stormRecord3 = StormRecord.builder()
                .id(storm3.getStormId())
                .stormName("storm3")
                .stormMode("removed")
                .creationDate(ZonedDateTime.now())
                .build();
        stormRecordRepository.saveAll(List.of(stormRecord1, stormRecord2, stormRecord3));
        stormRetrospectiveRepository.saveAll(List.of(storm1, storm2, storm3));

        testable.calculateStormSimilarities();

        storm1 = stormRetrospectiveRepository.findByStormId(storm1.getStormId()).get();
        storm2 = stormRetrospectiveRepository.findByStormId(storm2.getStormId()).get();

        assertThat(storm1.getSimilarStorms())
                .extracting(StormRetrospective.SimilarStorm::getStormId)
                .containsExactly(storm2.getStormId());

        assertThat(storm2.getSimilarStorms())
                .extracting(StormRetrospective.SimilarStorm::getStormId)
                .containsExactly(storm1.getStormId());
    }
}
