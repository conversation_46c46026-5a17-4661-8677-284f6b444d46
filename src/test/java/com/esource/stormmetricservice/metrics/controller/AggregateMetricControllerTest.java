package com.esource.stormmetricservice.metrics.controller;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.esource.stormmetricservice.aggregatemetrics.controller.AggregateMetricController;
import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric;
import com.esource.stormmetricservice.aggregatemetrics.model.AggregateStormMetric;
import com.esource.stormmetricservice.aggregatemetrics.repository.AggregateStormMetricRepository;
import com.esource.stormmetricservice.aggregatemetrics.service.AggregateMetricService;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class AggregateMetricControllerTest {

    @Mock
    private AggregateMetricService aggregateMetricService;

    @Mock
    private AggregateStormMetricRepository aggregateStormMetricRepository;

    @InjectMocks
    private AggregateMetricController aggregateMetricController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        mockMvc = MockMvcBuilders.standaloneSetup(aggregateMetricController)
                .setMessageConverters(new MappingJackson2HttpMessageConverter(objectMapper))
                .build();
    }

    @Test
    void testGetAggregateMetrics() {
        ZonedDateTime start = ZonedDateTime.parse("2021-01-01T12:00:00Z");
        ZonedDateTime end = ZonedDateTime.parse("2021-01-01T13:00:00Z");

        AggregateMetric expected =
                AggregateMetric.builder().startTime(start).endTime(end).build();

        when(aggregateMetricService.getAggregateMetrics(start, end, null, null)).thenReturn(expected);
        when(aggregateMetricService.filterAggregateMetrics(expected, null)).thenReturn(expected);
        when(aggregateMetricService.aggregateMetricsByGranularity(expected, null))
                .thenReturn(expected);

        ResponseEntity<AggregateMetric> actual =
                aggregateMetricController.getAggregateMetrics(start, end, null, null, null, null);
        ResponseEntity<AggregateMetric> expectedResponse = ResponseEntity.ok(expected);
        assertEquals(expectedResponse, actual);
    }

    @Test
    void testGetAggregateMetricsWithStormWindow() throws Exception {
        ZonedDateTime start = ZonedDateTime.parse("2021-01-01T12:00:00Z");
        ZonedDateTime end = ZonedDateTime.parse("2021-01-01T18:00:00Z");
        ZonedDateTime stormStart = ZonedDateTime.parse("2021-01-01T13:00:00Z");
        ZonedDateTime stormEnd = ZonedDateTime.parse("2021-01-01T16:00:00Z");
        List<String> regionsToFilter = null;
        mockMvc = MockMvcBuilders.standaloneSetup(aggregateMetricController).build();

        AggregateMetric expectedMetric = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(2))
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(3))
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(4))
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(5))
                                        .build())))
                .build();

        when(aggregateMetricService.getAggregateMetrics(start, end, stormStart, stormEnd))
                .thenReturn(expectedMetric);
        when(aggregateMetricService.filterAggregateMetrics(expectedMetric, regionsToFilter))
                .thenReturn(expectedMetric);
        when(aggregateMetricService.aggregateMetricsByGranularity(expectedMetric, 5))
                .thenReturn(expectedMetric);

        mockMvc.perform(get("/overview/aggregate")
                        .param("start", start.toString())
                        .param("end", end.toString())
                        .param("accumulationStart", stormStart.toString())
                        .param("accumulationEnd", stormEnd.toString()))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(expectedMetric)));
    }

    @Test
    void testGetAggregateMetricsWithRegionsToFilter() throws Exception {
        ZonedDateTime start = ZonedDateTime.parse("2021-01-01T12:00:00Z");
        ZonedDateTime end = ZonedDateTime.parse("2021-01-01T18:00:00Z");
        ZonedDateTime stormStart = ZonedDateTime.parse("2021-01-01T13:00:00Z");
        ZonedDateTime stormEnd = ZonedDateTime.parse("2021-01-01T16:00:00Z");
        List<String> regionsToFilter = List.of("Region1");
        mockMvc = MockMvcBuilders.standaloneSetup(aggregateMetricController).build();

        AggregateMetric expectedMetric = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(2))
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(3))
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(4))
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(5))
                                        .build())))
                .build();

        when(aggregateMetricService.getAggregateMetrics(start, end, stormStart, stormEnd))
                .thenReturn(expectedMetric);
        when(aggregateMetricService.filterAggregateMetrics(expectedMetric, regionsToFilter))
                .thenReturn(expectedMetric);
        when(aggregateMetricService.aggregateMetricsByGranularity(expectedMetric, 5))
                .thenReturn(expectedMetric);

        mockMvc.perform(get("/overview/aggregate")
                        .param("start", start.toString())
                        .param("end", end.toString())
                        .param("regionsToFilter", String.join(",", regionsToFilter))
                        .param("accumulationStart", stormStart.toString())
                        .param("accumulationEnd", stormEnd.toString()))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(expectedMetric)));
    }

    @Test
    void testGetStormAggregateMetric() throws Exception {
        String stormId = "testStormId";
        List<String> regionsToFilter = List.of("Region1");
        Integer outageGranularity = 5;

        ZonedDateTime start = ZonedDateTime.parse("2021-01-01T12:00:00Z");
        ZonedDateTime end = ZonedDateTime.parse("2021-01-01T18:00:00Z");

        AggregateMetric aggregateMetric = AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(Map.of(
                        "Region1",
                        List.of(
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start)
                                        .build(),
                                AggregateMetric.OverviewMetrics.builder()
                                        .timestamp(start.plusHours(1))
                                        .build())))
                .build();

        AggregateStormMetric aggregateStormMetric = AggregateStormMetric.builder()
                .id("testId")
                .stormOverview(StormOverview.builder().id(stormId).build())
                .aggregateMetric(aggregateMetric)
                .build();

        when(aggregateStormMetricRepository.findByStormOverviewId(stormId))
                .thenReturn(Optional.of(aggregateStormMetric));
        when(aggregateMetricService.filterAggregateMetrics(aggregateMetric, regionsToFilter))
                .thenReturn(aggregateMetric);
        when(aggregateMetricService.aggregateMetricsByGranularity(aggregateMetric, outageGranularity))
                .thenReturn(aggregateMetric);

        mockMvc.perform(get("/overview/stormAggregate")
                        .param("stormId", stormId)
                        .param("regionsToFilter", String.join(",", regionsToFilter))
                        .param("outageGranularity", outageGranularity.toString()))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(aggregateMetric)));
    }
}
