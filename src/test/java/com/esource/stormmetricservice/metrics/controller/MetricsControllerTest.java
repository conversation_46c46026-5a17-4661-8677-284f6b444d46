package com.esource.stormmetricservice.metrics.controller;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.service.MetricService;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class MetricsControllerTest {

    @Mock
    private MetricService metricService;

    @InjectMocks
    private MetricController metricController;

    @BeforeEach
    void setUp() {
        metricService = mock(MetricService.class);
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetOutageMetricByStormId_withStormMetricReturned() {

        when(metricService.getMetricByStormIdAndType("stormId", "outages", true))
                .thenReturn(Optional.of(StormMetric.builder().build()));

        // When
        ResponseEntity<StormMetric> response = metricController.getMetricsTypeByStormId("stormId", "outages", true);

        // Then
        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    void testGetOutageMetricByStormId_withStormMetricNotPresent() {

        when(metricService.getMetricByStormIdAndType("stormId", "outages", true))
                .thenReturn(Optional.empty());

        // When
        ResponseEntity<StormMetric> response = metricController.getMetricsTypeByStormId("stormId", "outages", true);

        // Then
        Assertions.assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
    }
}
