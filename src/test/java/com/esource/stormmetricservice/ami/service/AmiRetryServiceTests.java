package com.esource.stormmetricservice.ami.service;

import java.time.ZonedDateTime;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.TaskScheduler;

import com.esource.stormmetricservice.ami.model.AmiIngestionAttempt;
import com.esource.stormmetricservice.ami.model.AmiIngestionAttempt.IngestionStatus;
import com.esource.stormmetricservice.ami.repository.AmiIngestionAttemptRepository;
import com.esource.stormmetricservice.config.ami.AmiRetryProperties;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AmiRetryServiceTests {

    @Mock
    private AmiIngestionAttemptRepository attemptRepository;

    @Mock
    private AmiChangeIngestionQueueService queueService;

    @Mock
    private TaskScheduler taskScheduler;

    @Mock
    private AmiRetryProperties retryProperties;

    private AmiRetryService retryService;

    @BeforeEach
    void setUp() {
        retryService = new AmiRetryService(attemptRepository, queueService, retryProperties, taskScheduler);
    }

    @Test
    void initializeRetries_ShouldScheduleAllPendingRetries() {
        AmiIngestionAttempt attempt1 =
                createAttempt("STORM-1", ZonedDateTime.now().minusHours(1));
        AmiIngestionAttempt attempt2 =
                createAttempt("STORM-2", ZonedDateTime.now().plusHours(1));

        when(attemptRepository.findByStatus(IngestionStatus.NEEDS_RETRY)).thenReturn(List.of(attempt1, attempt2));

        retryService.initializeRetries();

        verify(taskScheduler, times(2)).schedule(any(Runnable.class), any(java.time.Instant.class));

        ArgumentCaptor<AmiIngestionAttempt> captor = ArgumentCaptor.forClass(AmiIngestionAttempt.class);
        verify(attemptRepository).save(captor.capture());

        AmiIngestionAttempt savedAttempt = captor.getValue();
        assertEquals(attempt1.getId(), savedAttempt.getId());
        assertTrue(savedAttempt.getNextAttemptTime().isAfter(ZonedDateTime.now()));
    }

    @Test
    void retryAttempt_WhenSuccessful_ShouldIncrementCountAndQueue() {
        AmiIngestionAttempt attempt = createAttempt("STORM-3", ZonedDateTime.now());
        attempt.setAttemptCount(1);

        retryService.retryAttempt(attempt);

        verify(queueService).queueStormEvent(attempt.getEvent());

        ArgumentCaptor<AmiIngestionAttempt> captor = ArgumentCaptor.forClass(AmiIngestionAttempt.class);
        verify(attemptRepository).save(captor.capture());

        AmiIngestionAttempt savedAttempt = captor.getValue();
        assertEquals(2, savedAttempt.getAttemptCount());
        assertNotNull(savedAttempt.getLastAttemptTime());
    }

    @Test
    void retryAttempt_WhenMaxRetriesExceeded_ShouldMarkAsFailed() {

        AmiIngestionAttempt attempt = createAttempt("STORM-4", ZonedDateTime.now());
        attempt.setAttemptCount(30);

        retryService.retryAttempt(attempt);

        ArgumentCaptor<AmiIngestionAttempt> captor = ArgumentCaptor.forClass(AmiIngestionAttempt.class);
        verify(attemptRepository).save(captor.capture());

        AmiIngestionAttempt savedAttempt = captor.getValue();
        assertEquals(IngestionStatus.FAILED, savedAttempt.getStatus());
        assertNull(savedAttempt.getNextAttemptTime());
    }

    @Test
    void retryAttempt_WhenErrorOccurs_ShouldScheduleNextRetry() {

        AmiIngestionAttempt attempt = createAttempt("STORM-5", ZonedDateTime.now());
        attempt.setAttemptCount(1);
        when(retryProperties.retryDelayHours()).thenReturn(1);
        doThrow(new RuntimeException("Queue error")).when(queueService).queueStormEvent(any());

        retryService.retryAttempt(attempt);

        ArgumentCaptor<AmiIngestionAttempt> captor = ArgumentCaptor.forClass(AmiIngestionAttempt.class);
        verify(attemptRepository, times(2)).save(captor.capture());

        AmiIngestionAttempt savedAttempt = captor.getValue();
        assertEquals(IngestionStatus.NEEDS_RETRY, savedAttempt.getStatus());
        assertNotNull(savedAttempt.getNextAttemptTime());
        verify(taskScheduler).schedule(any(Runnable.class), any(java.time.Instant.class));
    }

    @Test
    void retryAttempt_WhenStatusNotNeedsRetry_ShouldSkipProcessing() {

        AmiIngestionAttempt attempt = createAttempt("STORM-6", ZonedDateTime.now());
        attempt.setStatus(IngestionStatus.IN_PROGRESS);

        retryService.retryAttempt(attempt);

        verify(queueService, never()).queueStormEvent(any());
        verify(attemptRepository, never()).save(any());
    }

    private AmiIngestionAttempt createAttempt(String stormId, ZonedDateTime nextAttemptTime) {
        StormOverview stormData = StormOverview.builder()
                .id(stormId)
                .startDate(ZonedDateTime.now().minusDays(1))
                .build();

        EndStormEvent event = EndStormEvent.builder()
                .eventName("stormEndEvent")
                .timestamp(ZonedDateTime.now())
                .stormData(stormData)
                .build();

        return AmiIngestionAttempt.builder()
                .id(stormId)
                .stormId(stormId)
                .event(event)
                .status(IngestionStatus.NEEDS_RETRY)
                .nextAttemptTime(nextAttemptTime)
                .attemptCount(0)
                .build();
    }
}
