package com.esource.stormmetricservice.ami.service;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.esource.stormmetricservice.ami.model.AmiIngestionAttempt;
import com.esource.stormmetricservice.ami.model.AmiIngestionAttempt.IngestionStatus;
import com.esource.stormmetricservice.ami.model.AmiMeterChangeDocument;
import com.esource.stormmetricservice.ami.model.AmiMeterChangeDocument.MeterChange;
import com.esource.stormmetricservice.ami.repository.AmiChangeRepository;
import com.esource.stormmetricservice.ami.repository.AmiIngestionAttemptRepository;
import com.esource.stormmetricservice.config.ami.AmiRetryProperties;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AmiMeterChangeIngestionServiceTest {

    @Mock
    private AmiChangeRepository amiChangeRepository;

    @Mock
    private AmiIngestionAttemptRepository attemptRepository;

    @Mock
    private AmiQueryBuilder queryBuilder;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private AmiRetryProperties retryProperties;

    @Mock
    private Connection connection;

    @Mock
    private Statement statement;

    @Mock
    private ResultSet resultSet;

    @InjectMocks
    private AmiMeterChangeIngestionService service;

    @Captor
    private ArgumentCaptor<AmiIngestionAttempt> attemptCaptor;

    @Captor
    private ArgumentCaptor<List<AmiMeterChangeDocument>> documentsCaptor;

    private EndStormEvent endStormEvent;
    private StormOverview stormOverview;
    private static final String TEST_STORM_ID = "test-storm-123";
    private static final ZonedDateTime START_TIME = ZonedDateTime.now().minusDays(1);
    private static final ZonedDateTime END_TIME = ZonedDateTime.now();

    @BeforeEach
    void setUp() {
        stormOverview = StormOverview.builder()
                .id(TEST_STORM_ID)
                .startDate(START_TIME)
                .endDate(END_TIME)
                .name("Test Storm")
                .build();

        endStormEvent = EndStormEvent.builder()
                .eventName("stormEndEvent")
                .timestamp(ZonedDateTime.now())
                .stormData(stormOverview)
                .build();
    }

    @Test
    void testSuccessfulIngestion() throws SQLException, IOException {

        String testQuery = "SELECT * FROM test";
        when(queryBuilder.buildMeterChangesQuery(any(), any())).thenReturn(testQuery);
        when(connection.createStatement()).thenReturn(statement);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);

        when(resultSet.next()).thenReturn(true, true, false);
        when(resultSet.getTimestamp("timestamp")).thenReturn(new Timestamp(System.currentTimeMillis()));
        String meterChangesJson = "[{\"meterId\":\"meter1\",\"status\":1}]";
        when(resultSet.getString("metersChanged")).thenReturn(meterChangesJson);
        when(resultSet.getInt("number_of_changes")).thenReturn(1);

        List<MeterChange> meterChanges = new ArrayList<>();
        MeterChange change = new MeterChange();
        change.setMeterId("meter1");
        change.setStatus(1);
        meterChanges.add(change);

        doReturn(meterChanges).when(objectMapper).readValue(eq(meterChangesJson), any(TypeReference.class));

        when(retryProperties.minimumSuccessRate()).thenReturn(0.8);

        service.ingestAmiMeterChanges(endStormEvent, connection);

        verify(amiChangeRepository, times(1)).saveAll(documentsCaptor.capture());
        List<AmiMeterChangeDocument> savedDocuments = documentsCaptor.getValue();
        assertFalse(savedDocuments.isEmpty());
        assertEquals(2, savedDocuments.size());

        verify(attemptRepository, times(2)).save(attemptCaptor.capture());
        AmiIngestionAttempt finalAttempt = attemptCaptor.getValue();
        assertEquals(IngestionStatus.SUCCESSFUL, finalAttempt.getStatus());
        assertNull(finalAttempt.getNextAttemptTime());
    }

    @Test
    void testFailedIngestionWithRetry() throws SQLException {

        when(queryBuilder.buildMeterChangesQuery(any(), any())).thenReturn("SELECT * FROM test");
        when(connection.createStatement()).thenThrow(new SQLException("Database error"));
        when(retryProperties.retryDelayHours()).thenReturn(1);

        assertThrows(RuntimeException.class, () -> service.ingestAmiMeterChanges(endStormEvent, connection));

        verify(attemptRepository, times(2)).save(attemptCaptor.capture());
        AmiIngestionAttempt failedAttempt = attemptCaptor.getValue();

        assertEquals(IngestionStatus.FAILED, failedAttempt.getStatus());
        assertNotNull(failedAttempt.getNextAttemptTime());
        assertTrue(failedAttempt.getAttemptCount() > 0);
    }

    @Test
    void testPartialIngestionNeedsRetry() throws SQLException, IOException {

        String testQuery = "SELECT * FROM test";
        when(queryBuilder.buildMeterChangesQuery(any(), any())).thenReturn(testQuery);
        when(connection.createStatement()).thenReturn(statement);
        when(statement.executeQuery(anyString())).thenReturn(resultSet);

        when(resultSet.next()).thenReturn(true, true, true, false);

        when(resultSet.getTimestamp("timestamp"))
                .thenReturn(
                        new Timestamp(System.currentTimeMillis()),
                        new Timestamp(System.currentTimeMillis() + 3600000),
                        new Timestamp(System.currentTimeMillis() + 7200000));

        when(resultSet.getString("metersChanged")).thenReturn("[{\"meterId\":\"meter1\",\"status\":1}]", "[]", "[]");

        when(resultSet.getInt("number_of_changes")).thenReturn(1, 0, 0);

        List<MeterChange> meterChanges = new ArrayList<>();
        MeterChange change = new MeterChange();
        change.setMeterId("meter1");
        change.setStatus(1);
        meterChanges.add(change);

        doReturn(meterChanges)
                .when(objectMapper)
                .readValue(eq("[{\"meterId\":\"meter1\",\"status\":1}]"), any(TypeReference.class));

        doReturn(new ArrayList<MeterChange>()).when(objectMapper).readValue(eq("[]"), any(TypeReference.class));

        when(retryProperties.minimumSuccessRate()).thenReturn(0.9);
        when(retryProperties.retryDelayHours()).thenReturn(1);

        service.ingestAmiMeterChanges(endStormEvent, connection);

        verify(attemptRepository, times(2)).save(attemptCaptor.capture());
        AmiIngestionAttempt finalAttempt = attemptCaptor.getValue();

        assertEquals(IngestionStatus.NEEDS_RETRY, finalAttempt.getStatus());
        assertNotNull(finalAttempt.getNextAttemptTime());
        assertTrue(finalAttempt.getAttemptCount() > 0);

        verify(amiChangeRepository).saveAll(documentsCaptor.capture());
        List<AmiMeterChangeDocument> savedDocuments = documentsCaptor.getValue();
        assertEquals(3, savedDocuments.size());

        assertEquals(1.0 / 3.0, finalAttempt.getPercentDataPresent(), 0.01);
    }

    @Test
    void testCleanupExistingRecords() throws SQLException, IOException {

        List<AmiMeterChangeDocument> existingRecords = new ArrayList<>();
        existingRecords.add(new AmiMeterChangeDocument(START_TIME, new ArrayList<>()));

        when(amiChangeRepository.findMeterChangesByTimeRange(any(), any())).thenReturn(existingRecords);

        AmiIngestionAttempt existingAttempt = AmiIngestionAttempt.builder()
                .stormId(TEST_STORM_ID)
                .status(IngestionStatus.NEEDS_RETRY)
                .attemptCount(29)
                .event(endStormEvent)
                .build();

        when(attemptRepository.findByStormId(TEST_STORM_ID)).thenReturn(Optional.of(existingAttempt));

        String testQuery = "SELECT * FROM test";
        when(queryBuilder.buildMeterChangesQuery(any(), any())).thenReturn(testQuery);

        when(connection.createStatement()).thenReturn(statement);
        doReturn(resultSet).when(statement).executeQuery(eq(testQuery));
        when(resultSet.next()).thenReturn(false);

        when(retryProperties.minimumSuccessRate()).thenReturn(0.8);
        when(retryProperties.retryDelayHours()).thenReturn(1);

        service.ingestAmiMeterChanges(endStormEvent, connection);

        verify(amiChangeRepository).deleteByTimeRange(START_TIME, END_TIME);

        verify(attemptRepository, times(2)).save(attemptCaptor.capture());
        AmiIngestionAttempt finalAttempt = attemptCaptor.getValue();
        assertEquals(IngestionStatus.FAILED, finalAttempt.getStatus());
        assertEquals(30, finalAttempt.getAttemptCount());
        assertNull(finalAttempt.getNextAttemptTime());
    }

    @Test
    void testMaxRetriesReached() throws SQLException, IOException {

        AmiIngestionAttempt existingAttempt = AmiIngestionAttempt.builder()
                .stormId(TEST_STORM_ID)
                .status(IngestionStatus.NEEDS_RETRY)
                .attemptCount(30)
                .event(endStormEvent)
                .build();

        when(attemptRepository.findByStormId(TEST_STORM_ID)).thenReturn(Optional.of(existingAttempt));

        String testQuery = "SELECT * FROM test";
        when(queryBuilder.buildMeterChangesQuery(any(), any())).thenReturn(testQuery);

        when(connection.createStatement()).thenReturn(statement);
        doReturn(resultSet).when(statement).executeQuery(eq(testQuery));
        when(resultSet.next()).thenReturn(false);

        when(retryProperties.minimumSuccessRate()).thenReturn(0.8);
        when(retryProperties.retryDelayHours()).thenReturn(1);

        service.ingestAmiMeterChanges(endStormEvent, connection);

        verify(attemptRepository, times(2)).save(attemptCaptor.capture());
        List<AmiIngestionAttempt> savedAttempts = attemptCaptor.getAllValues();

        AmiIngestionAttempt firstSave = savedAttempts.get(0);

        AmiIngestionAttempt finalAttempt = savedAttempts.get(1);
        assertEquals(IngestionStatus.FAILED, finalAttempt.getStatus());
        assertNull(finalAttempt.getNextAttemptTime());
    }
}
