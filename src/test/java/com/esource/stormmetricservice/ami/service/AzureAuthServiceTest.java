package com.esource.stormmetricservice.ami.service;

import java.lang.reflect.Constructor;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.esource.stormmetricservice.ami.service.auth.AzureAuthService;
import com.esource.stormmetricservice.ami.service.auth.DatabricksAuthenticationException;
import com.esource.stormmetricservice.config.ami.AmiIngestionProperties;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

class AzureAuthServiceTest {

    @Mock
    private AmiIngestionProperties amiProperties;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private AzureAuthService azureAuthService;

    @Captor
    private ArgumentCaptor<HttpEntity<MultiValueMap<String, String>>> httpEntityCaptor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetAccessToken_Success() {
        // Setup test data
        String tenantId = "testTenantId";
        String clientId = "testClientId";
        String clientSecret = "testClientSecret";
        String scope = "testScope";
        String expectedAccessToken = "testAccessToken";

        AmiIngestionProperties.AzureAuth azureAuth =
                new AmiIngestionProperties.AzureAuth(tenantId, clientId, clientSecret, scope);
        when(amiProperties.azure()).thenReturn(azureAuth);

        when(restTemplate.postForEntity(any(String.class), any(HttpEntity.class), any()))
                .thenAnswer(invocation -> {
                    Class<?> responseType = invocation.getArgument(2);
                    Constructor<?> constructor = responseType.getDeclaredConstructor(String.class);
                    constructor.setAccessible(true);
                    Object tokenResponse = constructor.newInstance(expectedAccessToken);
                    return ResponseEntity.ok(tokenResponse);
                });

        String actualAccessToken = azureAuthService.getAccessToken();
        assertNotNull(actualAccessToken);
        assertEquals(expectedAccessToken, actualAccessToken);
    }

    @Test
    void testGetAccessToken_NullResponseBody() {
        String tenantId = "testTenantId";
        String clientId = "testClientId";
        String clientSecret = "testClientSecret";
        String scope = "testScope";

        AmiIngestionProperties.AzureAuth azureAuth =
                new AmiIngestionProperties.AzureAuth(tenantId, clientId, clientSecret, scope);
        when(amiProperties.azure()).thenReturn(azureAuth);

        String tokenUrl = String.format("https://login.microsoftonline.com/%s/oauth2/v2.0/token", tenantId);
        ResponseEntity<Object> responseEntity = new ResponseEntity<>(null, HttpStatus.OK);

        when(restTemplate.postForEntity(eq(tokenUrl), any(HttpEntity.class), any(Class.class)))
                .thenReturn(responseEntity);

        assertThrows(DatabricksAuthenticationException.class, () -> azureAuthService.getAccessToken());
    }

    @Test
    void testGetAccessToken_RestTemplateException() {
        String tenantId = "testTenantId";
        String clientId = "testClientId";
        String clientSecret = "testClientSecret";
        String scope = "testScope";

        AmiIngestionProperties.AzureAuth azureAuth =
                new AmiIngestionProperties.AzureAuth(tenantId, clientId, clientSecret, scope);
        when(amiProperties.azure()).thenReturn(azureAuth);

        String tokenUrl = String.format("https://login.microsoftonline.com/%s/oauth2/v2.0/token", tenantId);

        when(restTemplate.postForEntity(eq(tokenUrl), any(HttpEntity.class), any(Class.class)))
                .thenThrow(new RuntimeException("Test Exception"));

        assertThrows(DatabricksAuthenticationException.class, () -> azureAuthService.getAccessToken());
    }
}
