package com.esource.stormmetricservice.ami.service;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.ZonedDateTime;
import java.util.concurrent.TimeUnit;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.esource.stormmetricservice.ami.service.auth.DatabricksConnectionService;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class AmiChangeIngestionQueueServiceTest {

    @Mock
    private AmiMeterChangeIngestionService ingestionService;

    @Mock
    private DatabricksConnectionService connectionService;

    @Mock
    private Connection mockConnection;

    @Mock
    private Statement mockStatement;

    private AmiChangeIngestionQueueService queueService;

    @BeforeEach
    void setUp() throws SQLException {
        MockitoAnnotations.openMocks(this);

        when(connectionService.createConnection()).thenReturn(mockConnection);
        when(mockConnection.createStatement()).thenReturn(mockStatement);
        when(mockStatement.execute(anyString())).thenReturn(true);

        queueService = new AmiChangeIngestionQueueService(ingestionService, connectionService);
        queueService.init();
    }

    @Test
    void testSuccessfulEventProcessing() throws Exception {
        EndStormEvent event = createTestEvent();
        queueService.queueStormEvent(event);

        Awaitility.await().atMost(1, TimeUnit.SECONDS).untilAsserted(() -> verify(ingestionService)
                .ingestAmiMeterChanges(eq(event), any(Connection.class)));
        verify(ingestionService).ingestAmiMeterChanges(eq(event), any(Connection.class));
        verify(mockConnection, never()).close();
    }

    @Test
    void testConnectionRefreshOnFailure() throws Exception {
        EndStormEvent event = createTestEvent();
        when(mockStatement.execute("SELECT 1"))
                .thenThrow(new SQLException("Connection lost"))
                .thenReturn(true);

        queueService.queueStormEvent(event);

        Awaitility.await().atMost(1, TimeUnit.SECONDS).untilAsserted(() -> {
            verify(connectionService, times(2)).createConnection();
            verify(ingestionService).ingestAmiMeterChanges(eq(event), any(Connection.class));
        });
    }

    @Test
    void testGracefulShutdown() throws Exception {
        EndStormEvent event = createTestEvent();
        queueService.queueStormEvent(event);

        queueService.shutdown();

        verify(mockConnection, times(1)).close();
    }

    @Test
    void testQueueingMultipleEvents() throws Exception {
        EndStormEvent event1 = createTestEvent();
        EndStormEvent event2 = createTestEvent();

        queueService.queueStormEvent(event1);
        queueService.queueStormEvent(event2);

        Awaitility.await().atMost(2, TimeUnit.SECONDS).untilAsserted(() -> verify(ingestionService, times(2))
                .ingestAmiMeterChanges(any(EndStormEvent.class), any(Connection.class)));
    }

    @Test
    void testConnectionValidationBeforeProcessing() throws Exception {
        EndStormEvent event = createTestEvent();
        when(mockConnection.isClosed()).thenReturn(true, false);
        when(connectionService.createConnection()).thenReturn(mockConnection);

        queueService.queueStormEvent(event);

        Awaitility.await().atMost(1, TimeUnit.SECONDS).untilAsserted(() -> {
            verify(connectionService, times(2)).createConnection(); // Initial + refresh
            verify(ingestionService).ingestAmiMeterChanges(eq(event), any(Connection.class));
        });
    }

    @Test
    void testHandlingIngestionServiceException() throws Exception {
        EndStormEvent event = createTestEvent();
        doThrow(new RuntimeException("Ingestion failed")).when(ingestionService).ingestAmiMeterChanges(any(), any());

        queueService.queueStormEvent(event);

        Awaitility.await().atMost(1, TimeUnit.SECONDS).untilAsserted(() -> {
            verify(ingestionService).ingestAmiMeterChanges(any(), any());
            verify(connectionService, times(2)).createConnection();
        });
    }

    private EndStormEvent createTestEvent() {
        return EndStormEvent.builder()
                .eventName("stormEndEvent")
                .timestamp(ZonedDateTime.now())
                .stormData(StormOverview.builder()
                        .id("test-storm-id")
                        .startDate(ZonedDateTime.now().minusHours(1))
                        .endDate(ZonedDateTime.now())
                        .name("Test Storm")
                        .build())
                .build();
    }
}
