WITH intervals AS (
    SELECT explode(sequence(
        to_timestamp(:startTime),
        to_timestamp(:endTime),
        interval 5 minutes
    )) AS timestamp_bucket
),
meter_events AS (
    SELECT
        CAST(FLOOR(UNIX_TIMESTAMP(e.event_start_toe_utc) / 300) * 300 AS timestamp) AS event_bucket,
        CAST(m.FLDMETERID AS STRING) AS meterId,
        CAST(
            CASE
                WHEN e.event_composite_id = 100024019001007 THEN 0  -- Power Failure
                WHEN e.event_composite_id = 100024048001007 THEN 1  -- Power Restore
            END
        AS BIGINT) AS status,
        e.event_start_toe_utc
    FROM apc_meter.ami_event_history e
    JOIN apc_meter.ami_meter_list m
        ON m.FLDREPID = e.endpoint_id
    WHERE e.event_composite_id IN (100024019001007, 100024048001007)
        AND e.event_start_toe_utc BETWEEN to_timestamp(:startTime) AND to_timestamp(:endTime)
        AND m.FLDMETERLIFECYCLESTATECODE = 'Install'
        AND m.FLDLAT IS NOT NULL
        AND m.FLDLONG IS NOT NULL
        AND m.FLDMETERID NOT IN (
            SELECT fldmeterid FROM apc_meter.ami_cutlist
        )
),
final_meter_states AS (
    SELECT DISTINCT
        event_bucket,
        meterId,
        CAST(
            LAST_VALUE(status) OVER (
                PARTITION BY event_bucket, meterId
                ORDER BY event_start_toe_utc
                ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
            ) AS BIGINT
        ) AS final_status
    FROM meter_events
),
interval_changes AS (
    SELECT
        event_bucket AS timestamp,
        collect_list(
            struct(
                meterId AS meterId,
                CAST(final_status AS BIGINT) AS status
            )
        ) AS metersChanged
    FROM final_meter_states
    GROUP BY event_bucket
)
SELECT
    i.timestamp_bucket AS timestamp,
    COALESCE(ic.metersChanged, array()) AS metersChanged,
    SIZE(COALESCE(ic.metersChanged, array())) AS number_of_changes
FROM intervals i
LEFT JOIN interval_changes ic
    ON i.timestamp_bucket = ic.timestamp
ORDER BY i.timestamp_bucket;