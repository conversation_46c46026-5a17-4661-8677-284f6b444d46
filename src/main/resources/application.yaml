#spring:
#  application:
#    name: storm-metric-service
#  data:
#    rest:
#      default-page-size: 100
#    mongodb:
#      autoIndexCreation: true
#    web:
#      pageable:
#        default-page-size: 100
#  threads:
#    virtual:
#      enabled: true
#management:
#  endpoints:
#    web:
#      exposure:
#        include: "*"
#  endpoint:
#    health:
#      show-details: always
#
#springdoc:
#  swagger-ui:
#    enabled: false
#rabbitmq:
#  exchange:
#    stormEvent: storm.storm-event-exchange
#  queue:
#    stormEnd: storm.storm-event.metric-service.storm-end.queue
#    aggregateOverview: storm.storm-event.metric-service.aggregate-overview.queue
#  routingKey:
#    routingKey: storm.storm-event.metric-service.#
#    aggregateRoutingKey: storm.storm-aggregate-event.metric-service.#
#
#metrics:
#  aggregator:
#    cumulative-outage.strategy: delta # default == delta
#    look-back-minutes: 2
#  unaggregated-outage:
#    regions: Alpharetta Area,Athens,Atlanta,Augusta,Columbus,Jonesboro,Macon,Rome,Savannah,Smyrna,Valdosta
#
#server:
#  compression:
#    enabled: true
#    mime-types: application/json, application/xml, text/html, text/plain
#    min-response-size: 1024