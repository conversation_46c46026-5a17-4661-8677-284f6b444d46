package com.esource.stormmetricservice.etr.repository;

import java.util.Optional;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.etr.model.ResourceEntity;

@Repository
public interface ResourceRepository extends MongoRepository<ResourceEntity, String> {
    Optional<ResourceEntity> findFirstBySessionIdOrderByCreationTimeDesc(ObjectId sessionId);

    Optional<ResourceEntity> findFirstByStormIdAndGenerationTypeOrderByCreationTimeDesc(
            String stormId, String generationType);

    Optional<ResourceEntity> findFirstByStormIdAndGenerationTypeAndSessionIdOrderByCreationTimeDesc(
            String stormId, String generationType, ObjectId sessionId);

    Optional<ResourceEntity> findFirstByGenerationTypeOrderByCreationTimeDesc(String generationType);
}
