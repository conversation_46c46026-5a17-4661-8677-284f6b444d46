package com.esource.stormmetricservice.etr.repository;

import java.time.ZonedDateTime;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.esource.stormmetricservice.etr.model.StormRecord;

public interface StormRecordRepository extends MongoRepository<StormRecord, String> {

    @Query("{ $and: [ " + "{ 'stormMode': { $ne: 'archive' } }, "
            + "{ 'stormMode': { $ne: 'removed' } }, "
            + "{ $or: [ { 'stormEndDate': null }, { 'expirationDate': { $gte: ?0 } } ] } "
            + "] }")
    List<StormRecord> findActiveNonArchivedStorms(ZonedDateTime currentTime);

    List<StormRecord> findAllByOrderByCreationDateDesc();

    StormRecord findByStormName(String stormName);

    List<StormRecord> findByStormMode(String stormMode);
}
