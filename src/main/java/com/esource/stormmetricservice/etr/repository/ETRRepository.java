package com.esource.stormmetricservice.etr.repository;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.etr.model.ETR;

@Repository
public interface ETRRepository extends MongoRepository<ETR, String> {

    List<ETR> findByStormIdAndEtrType(String stormId, String etrType);
}
