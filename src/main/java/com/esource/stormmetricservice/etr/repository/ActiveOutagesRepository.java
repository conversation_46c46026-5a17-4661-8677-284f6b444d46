package com.esource.stormmetricservice.etr.repository;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;

import com.esource.stormmetricservice.etr.model.ActiveOutages;

public interface ActiveOutagesRepository extends MongoRepository<ActiveOutages, String> {

    Optional<ActiveOutages> findFirstByCreatedTimestampLessThanEqualOrderByCreatedTimestampDesc(
            ZonedDateTime timestamp);

    Optional<ActiveOutages> findFirstByCreatedTimestampLessThanOrderByCreatedTimestampDesc(ZonedDateTime timestamp);

    List<ActiveOutages> findByCreatedTimestampBetween(ZonedDateTime start, ZonedDateTime end);
}
