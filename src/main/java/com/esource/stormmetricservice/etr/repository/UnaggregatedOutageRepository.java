package com.esource.stormmetricservice.etr.repository;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.etr.model.UnaggregatedOutage;

@Repository
@RepositoryRestResource
public interface UnaggregatedOutageRepository extends MongoRepository<UnaggregatedOutage, ObjectId> {

    List<UnaggregatedOutage> findByCreatedTimestampBetweenOrderByCreatedTimestampAsc(
            ZonedDateTime start, ZonedDateTime end);

    Optional<UnaggregatedOutage> findFirstByCreatedTimestampLessThanOrderByCreatedTimestampDesc(
            ZonedDateTime timestamp);

    Optional<UnaggregatedOutage> findFirstByCreatedTimestampLessThanEqualOrderByCreatedTimestampDesc(
            ZonedDateTime timestamp);
}
