package com.esource.stormmetricservice.etr.service;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.etr.model.ResourceEntity;
import com.esource.stormmetricservice.etr.model.TerritoryTimeKey;
import com.esource.stormmetricservice.etr.model.dto.AggregatedResourcesDTO;
import com.esource.stormmetricservice.etr.repository.ResourceRepository;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class ResourceService {

    private static final ZonedDateTime MIN_DATE = ZonedDateTime.of(1900, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC);
    private final ResourceRepository resourcesRepository;

    public List<AggregatedResourcesDTO> getCurrentResources(
            String stormId, ZonedDateTime now, String generationType, String sessionId) {
        return generateAggregatedResourcesDTO(
                stormId,
                timeframe -> (timeframe.getArrivalTime() == null
                                || timeframe.getArrivalTime().isBefore(now))
                        && (timeframe.getDepartureTime() == null
                                || timeframe.getDepartureTime().isAfter(now)),
                false,
                true,
                generationType,
                sessionId);
    }

    public List<AggregatedResourcesDTO> getAllResources(
            String stormId, ZonedDateTime currentTime, String generationType, String sessionId) {
        return generateAggregatedResourcesDTO(stormId, timeframe -> true, true, true, generationType, sessionId);
    }

    public List<AggregatedResourcesDTO> getFutureResources(
            String stormId, ZonedDateTime currentTime, String generationType, String sessionId) {
        return generateAggregatedResourcesDTO(
                stormId,
                timeframe -> timeframe.getArrivalTime() != null
                        && timeframe.getArrivalTime().isAfter(currentTime),
                true,
                false,
                generationType,
                sessionId);
    }

    public Optional<ResourceEntity> getLatestResourceBySessionId(ObjectId sessionId) {
        return resourcesRepository.findFirstBySessionIdOrderByCreationTimeDesc(sessionId);
    }

    public Optional<ResourceEntity> getLatestResourceByGenerationType(String generationType) {
        return resourcesRepository.findFirstByGenerationTypeOrderByCreationTimeDesc(generationType);
    }

    public Optional<ResourceEntity> getLatestResourceByStormId(String stormId, String generationType) {
        return resourcesRepository.findFirstByStormIdAndGenerationTypeOrderByCreationTimeDesc(stormId, generationType);
    }

    private List<AggregatedResourcesDTO> generateAggregatedResourcesDTO(
            String stormId,
            Predicate<ResourceEntity.ResourceTimeframe> timeframeFilter,
            boolean groupByArrivalTime,
            boolean returnDefaultIfEmpty,
            String generationType,
            String sessionId) {

        Optional<ResourceEntity> optionalResource;
        if (null != sessionId && !sessionId.isBlank()) {
            optionalResource =
                    resourcesRepository.findFirstByStormIdAndGenerationTypeAndSessionIdOrderByCreationTimeDesc(
                            stormId, generationType, new ObjectId(sessionId));
        } else {
            optionalResource = getLatestResourceByStormId(stormId, generationType);
        }
        if (optionalResource.isPresent()) {
            return optionalResource.get().getSystemResources().stream()
                    .flatMap(
                            sysRes -> aggregateTerritoryResources(sysRes, timeframeFilter, groupByArrivalTime).stream())
                    .toList();
        } else if (returnDefaultIfEmpty) {
            Optional<ResourceEntity> defaultResource =
                    resourcesRepository.findFirstByGenerationTypeOrderByCreationTimeDesc(
                            ResourceEntity.ResourceConstants.GENERATION_TYPE_DEFAULT);
            if (defaultResource.isPresent()) {
                return defaultResource.get().getSystemResources().stream()
                        .flatMap(sysRes ->
                                aggregateTerritoryResources(sysRes, timeframeFilter, groupByArrivalTime).stream())
                        .toList();
            }
        }

        return Collections.emptyList();
    }

    private List<AggregatedResourcesDTO> aggregateTerritoryResources(
            ResourceEntity.SystemResources sysRes,
            Predicate<ResourceEntity.ResourceTimeframe> timeframeFilter,
            boolean groupByArrivalTime) {

        Map<TerritoryTimeKey, List<ResourceEntity.ResourceTimeframe>> groupedTimeframes =
                groupTimeframes(sysRes, timeframeFilter, groupByArrivalTime);
        return createAggregatedResources(groupedTimeframes, sysRes);
    }

    private Map<TerritoryTimeKey, List<ResourceEntity.ResourceTimeframe>> groupTimeframes(
            ResourceEntity.SystemResources sysRes,
            Predicate<ResourceEntity.ResourceTimeframe> timeframeFilter,
            boolean groupByArrivalTime) {

        return sysRes.getResourcesTimeframes().stream()
                .filter(timeframeFilter)
                .collect(Collectors.groupingBy(tf -> TerritoryTimeKey.builder()
                        .arrivalTime(groupByArrivalTime ? tf.getArrivalTime() : null)
                        .territoryId(sysRes.getTerritoryId())
                        .build()));
    }

    private List<AggregatedResourcesDTO> createAggregatedResources(
            Map<TerritoryTimeKey, List<ResourceEntity.ResourceTimeframe>> groupedTimeframes,
            ResourceEntity.SystemResources sysRes) {

        return groupedTimeframes.entrySet().stream()
                .map(entry -> createAggregatedResourceDTO(
                        entry.getKey().getArrivalTime() == null
                                ? null
                                : entry.getKey().getArrivalTime(),
                        sysRes.getTerritoryName(),
                        entry.getKey().getTerritoryId(),
                        aggregateResources(entry.getValue())))
                .sorted(Comparator.comparing(dto -> dto.getArrivalTime().orElse(MIN_DATE)))
                .toList();
    }

    private AggregatedResourcesDTO createAggregatedResourceDTO(
            ZonedDateTime arrivalTime, String territoryName, String territoryId, Map<String, Integer> totalResources) {
        return AggregatedResourcesDTO.builder()
                .arrivalTime(Optional.ofNullable(arrivalTime))
                .territoryName(territoryName)
                .territoryId(territoryId)
                .totalResources(totalResources)
                .build();
    }

    private Map<String, Integer> aggregateResources(List<ResourceEntity.ResourceTimeframe> timeframes) {
        return timeframes.stream()
                .flatMap(tf -> tf.getResources().entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, Integer::sum));
    }
}
