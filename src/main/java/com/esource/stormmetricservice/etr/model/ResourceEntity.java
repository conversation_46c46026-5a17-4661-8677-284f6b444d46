package com.esource.stormmetricservice.etr.model;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document(collection = "resources")
public class ResourceEntity {
    @Id
    String id;

    ObjectId sessionId;

    ZonedDateTime stormStart;

    String stormId;

    ZonedDateTime creationTime;

    String createdBy;

    String generationType;

    List<SystemResources> systemResources;

    @Data
    @Builder
    public static class SystemResources {
        String territoryId;

        String territoryName;

        Map<String, Integer> resources;

        Integer totalResources;

        List<ResourceTimeframe> resourcesTimeframes;
    }

    @Data
    @Builder
    public static class ResourceTimeframe {
        ZonedDateTime arrivalTime;
        ZonedDateTime departureTime;
        Map<String, Integer> resources;
    }

    public static class ResourceConstants {
        public static final String GENERATION_TYPE_MANUAL = "manual";
        public static final String GENERATION_TYPE_DEFAULT = "default";

        public static final String GENERATION_TYPE_SIMULATION = "simulation";
    }
}
