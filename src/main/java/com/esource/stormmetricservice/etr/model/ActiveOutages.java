package com.esource.stormmetricservice.etr.model;

import java.time.ZonedDateTime;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document(collection = "active_outages")
public class ActiveOutages {
    @Id
    private String id;

    @Field("session")
    private ObjectId sessionId;

    private ZonedDateTime lastUpdated;

    @Indexed(name = "outage_time_query", direction = IndexDirection.DESCENDING)
    private ZonedDateTime createdTimestamp;

    private String generatedBy;
    private List<OutageRecord> outageRecords;

    @Data
    @Builder
    public static class OutageRecord {
        String region;
        Integer activeCustomerOutages;
        Integer activeIncidents;
        Integer restoredCustomerOutages;
        Integer restoredIncidents;

        Integer rawActiveCustomerOutages;
        Integer rawActiveIncidents;
        Integer rawRestoredCustomerOutages;
        Integer rawRestoredIncidents;
    }
}
