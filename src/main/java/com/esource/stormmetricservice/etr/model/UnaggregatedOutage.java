package com.esource.stormmetricservice.etr.model;

import java.time.ZonedDateTime;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document("historical_outages")
public class UnaggregatedOutage {

    @Id
    private ObjectId id;

    @Field("session")
    private ObjectId sessionId;

    private ZonedDateTime lastUpdated;

    private ZonedDateTime dataEntryDate;

    @Indexed(name = "outage_time_query", direction = IndexDirection.DESCENDING)
    private ZonedDateTime createdTimestamp;

    private String generationTime;

    @Field("outageRecords")
    private List<RawOutage> rawOutages;

    @Builder
    @Data
    public static class RawOutage {

        private String incidentName;
        private String region;
        private Integer currentCustomers;
        private Integer affectedCustomers;
        private ZonedDateTime startTime;
    }
}
