package com.esource.stormmetricservice.etr.model;

import java.time.ZonedDateTime;
import java.util.*;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import jakarta.validation.constraints.Pattern;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document(collection = "etr_document")
public class ETR {
    @Field("session")
    ObjectId sessionId;

    Map<String, Integer> systemResources;

    List<Resources> resources;

    String stormId;

    ZonedDateTime generationTime;

    String etrType;

    @Field("stormStartDate")
    ZonedDateTime stormStartDate;

    @Builder.Default
    List<RegionalETR> regionalETRs = new ArrayList<>();

    @Builder.Default
    Map<String, ZonedDateTime> systemRestoration = new HashMap<>();

    Map<String, ZonedDateTime> systemAssessment = new HashMap<>();

    @Data
    @Builder
    public static class RegionalETR {
        String location;

        String outageScale; // low medium and high for forecasting
        Double activeOutages; // these four fields are for current ETR, activeOutages/Incidents is overwritten
        Double activeCustOutages; // active customers out
        Double remainingOutages;
        Double remainingCustOutages;

        ZonedDateTime projectedETR;
        ZonedDateTime projectedETA;

        @Builder.Default
        Map<String, Integer> resources = new HashMap<>();

        // type can only be simulation or current
        @Pattern(regexp = "simulation|current") String type;

        public static class Type {
            public static final String SIMULATION = "simulation";
            public static final String CURRENT = "current";
        }
    }

    @Data
    @Builder
    public static class Resources {
        String territoryId;

        String territoryName;

        Map<String, Integer> resources;
    }
}
