package com.esource.stormmetricservice.etr.model;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.fasterxml.jackson.annotation.JsonIgnore;

import jakarta.validation.constraints.Pattern;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document(collection = "storm_record")
public class StormRecord {

    @Id
    String id;

    String stormName;

    ZonedDateTime stormStartDate;

    ZonedDateTime stormEndDate;
    ZonedDateTime projectedETA;

    ZonedDateTime expirationDate;

    @JsonIgnore
    @Builder.Default
    Map<String, Object> metadata = new HashMap<>();

    ZonedDateTime creationDate;

    String displayName;

    @Pattern(regexp = "manual|system") String generationType;

    @Builder.Default
    Map<String, ZonedDateTime> projectedRegionalRestoration = new HashMap<>();

    Map<String, ZonedDateTime> projectedRegionalAssessment = new HashMap<>();

    @Pattern(regexp = "archive|active|forecast|post-restoration|removed") String stormMode;
}
