package com.esource.stormmetricservice.aggregatemetrics.repository;

import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateStormMetric;

@Repository
@RepositoryRestResource(collectionResourceRel = "aggregateStormMetrics", path = "aggregateStormMetrics")
public interface AggregateStormMetricRepository extends MongoRepository<AggregateStormMetric, String> {

    Optional<AggregateStormMetric> findByStormOverviewId(String stormId);
}
