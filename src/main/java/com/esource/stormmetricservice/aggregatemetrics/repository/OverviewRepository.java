package com.esource.stormmetricservice.aggregatemetrics.repository;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.aggregatemetrics.model.Overview;

@Repository
public interface OverviewRepository extends MongoRepository<Overview, String> {

    List<Overview> findByTimestampBetweenOrderByTimestampAsc(ZonedDateTime start, ZonedDateTime end);

    Optional<Overview> findByTimestamp(ZonedDateTime timestamp);

    void deleteByTimestamp(ZonedDateTime timestamp);

    @Aggregation(
            pipeline = {
                """
                            {
                                $match: {
                                    "timestamp": {
                                        $gte: ?0,
                                        $lte: ?1
                                    }
                                }
                            }
                            """,
                """
                            {
                                $addFields: {
                                    "rounded_ts": {
                                        $dateTrunc: { "date": "$timestamp", "unit": "hour" }
                                    }
                                }
                            }
                            """,
                """
                            {
                                $lookup: {
                                    from: "weather",
                                    let: { "overview_ts": "$rounded_ts" },
                                    pipeline: [
                                        {
                                            "$match": {
                                                  "timestamp": {
                                                    "$gte": ?2,
                                                    "$lte": ?3
                                                  }
                                            }
                                        },
                                        {
                                            $addFields: {
                                                "rounded_ts": {
                                                    $dateTrunc: { "date": "$timestamp", "unit": "hour" }
                                                }
                                            }
                                        },
                                        {
                                            $match: {
                                                $expr: { $eq: ["$rounded_ts", "$$overview_ts"] }
                                            }
                                        },
                                        {
                                            $replaceRoot: {
                                                newRoot: {
                                                    $mergeObjects: ["$weatherStations", { "_id": "$_id" }]
                                                }
                                            }
                                        }
                                    ],
                                    as: "stationData"
                                }
                            }
                            """,
                """
                            {
                                $project: {
                                    "_id": 1,
                                    "data": {
                                        "outages": "$data.outages",
                                        "resources": "$data.resources",
                                        "stationData": "$stationData"
                                    },
                                    "timestamp": 1
                                }
                            }
                            """,
                """
                            {
                                $sort: { "timestamp": 1 }
                            }
                            """
            })
    List<Overview> findByTimestampBetweenWithWeather(
            ZonedDateTime start, ZonedDateTime end, ZonedDateTime startTruncToHour, ZonedDateTime endTruncToHour);
}
