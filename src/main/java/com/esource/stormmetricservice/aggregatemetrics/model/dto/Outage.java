package com.esource.stormmetricservice.aggregatemetrics.model.dto;

import java.util.HashMap;
import java.util.Map;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class Outage {
    private int totalIncidents;
    private int totalAffectedCustomers;
    private Integer totalRawIncidents;
    private Integer totalRawAffectedCustomers;
    private int uniqueIncidentsCount;
    private int uniqueAffectedCustomers;

    @Builder.Default
    private Map<String, Integer> outageIncidentMap = new HashMap<>(); // {incidentId : affectedCustomers}
}
