package com.esource.stormmetricservice.aggregatemetrics.model;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.TimeSeries;
import org.springframework.data.mongodb.core.timeseries.Granularity;

import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Resource;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@TimeSeries(collection = "overview", timeField = "timestamp", granularity = Granularity.MINUTES, metaField = "data")
public class Overview {

    @Id
    String id;

    @Indexed(direction = IndexDirection.ASCENDING)
    ZonedDateTime timestamp;

    OverviewData data;

    @Data
    @Builder
    public static class OverviewData {

        Map<String, Outage> outages;

        Map<String, Resource> resources;

        List<Object> stationData; // ideally this can be mapped in the future
    }
}
