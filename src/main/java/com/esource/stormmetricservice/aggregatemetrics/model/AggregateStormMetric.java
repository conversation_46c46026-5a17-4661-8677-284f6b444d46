package com.esource.stormmetricservice.aggregatemetrics.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

import com.esource.stormmetricservice.metrics.model.StormOverview;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document("historical_aggregate_metric")
@CompoundIndex(name = "storm_overview", def = "{'stormOverview._id': 1}")
public class AggregateStormMetric {

    @Id
    private String id;

    private StormOverview stormOverview;

    private AggregateMetric aggregateMetric;
}
