package com.esource.stormmetricservice.aggregatemetrics.model;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AggregateMetric {
    private Map<String, List<OverviewMetrics>> regionalMetrics;
    private ZonedDateTime startTime;
    private ZonedDateTime endTime;

    @Builder
    @Data
    public static class OverviewMetrics {
        private ZonedDateTime timestamp;
        // outages
        private int totalIncidents;
        private int totalAffectedCustomers;
        private Integer totalRawIncidents;
        private Integer totalRawAffectedCustomers;

        private int uniqueIncidentsCount;
        private int uniqueAffectedCustomers;
        // resources
        private int totalResources;
        private Map<String, Integer> resources;

        // weather
        private double windGustMph;

        private double averageWindSpeed;

        // Calculated fields
        private int totalRestorations;
        private int totalAffectedCustomerRestorations;

        private int cumulativeAffectedCustomers;
        private int cumulativeRestorations;
        private int cumulativeIncidents;
        private int cumulativeCustomersRestored;

        @JsonIgnore
        @Builder.Default
        private Map<String, Integer> outageIncidentMap = new HashMap<>(); // {incidentId : affectedCustomers}

        private double restoredPerWorker;
        private Map<String, Integer> windGustThresholds; // {"over30", "over40", "over50"}
    }
}
