package com.esource.stormmetricservice.aggregatemetrics.events;

import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.aggregatemetrics.service.OverviewAggregator;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEventProcessed;

import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AggregateOverviewListener {

    private final OverviewAggregator overviewAggregator;

    @EventListener
    public void onAggregateOverviewEventProcessed(AggregateOverviewEventProcessed event) {
        overviewAggregator.goCollectMetrics(event.getAggregateOverviewEvent());
    }
}
