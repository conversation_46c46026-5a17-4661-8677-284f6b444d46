package com.esource.stormmetricservice.aggregatemetrics.service.weather;

import java.util.List;

import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.weather.model.RawWeatherData.WeatherStation;

import static com.esource.stormmetricservice.common.util.MetricConstants.SYSTEM_REGION;

/**
 * Default weather filtering strategy that filters weather stations by region.
 * This is the default behavior that was previously used.
 */
@Component
public class RegionBasedWeatherFilteringStrategy implements WeatherFilteringStrategy {
    
    @Override
    public List<WeatherStation> filterStations(List<WeatherStation> stations, String region) {
        return stations.stream()
                .filter(station -> station.getRegion().equals(region) || region.equals(SYSTEM_REGION))
                .toList();
    }
    
    @Override
    public String getGroupingKey(WeatherStation station) {
        return station.getRegion();
    }
}
