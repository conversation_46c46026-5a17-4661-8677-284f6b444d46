package com.esource.stormmetricservice.aggregatemetrics.service.weather;

import java.util.List;

import com.esource.stormmetricservice.weather.model.RawWeatherData.WeatherStation;

/**
 * Strategy interface for filtering weather stations based on different criteria.
 */
public interface WeatherFilteringStrategy {
    
    /**
     * Filters weather stations based on the strategy's criteria.
     * 
     * @param stations List of weather stations to filter
     * @param filterValue The value to filter by (region name, workgroup name, etc.)
     * @return Filtered list of weather stations
     */
    List<WeatherStation> filterStations(List<WeatherStation> stations, String filterValue);
    
    /**
     * Gets the grouping key for a weather station based on the strategy.
     * 
     * @param station The weather station
     * @return The key to group by
     */
    String getGroupingKey(WeatherStation station);
}
