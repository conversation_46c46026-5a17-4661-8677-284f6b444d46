package com.esource.stormmetricservice.aggregatemetrics.service.weather;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.config.weather.WeatherFilteringProperties;
import com.esource.stormmetricservice.weather.model.RawWeatherData.WeatherStation;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service responsible for determining the appropriate weather filtering strategy
 * and applying it based on client configuration.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeatherFilteringService {
    
    private final WeatherFilteringProperties weatherFilteringProperties;
    private final RegionBasedWeatherFilteringStrategy regionBasedStrategy;
    private final WorkgroupBasedWeatherFilteringStrategy workgroupBasedStrategy;
    
    /**
     * Determines the appropriate filtering strategy and groups weather stations accordingly.
     * 
     * @param stations List of weather stations to process
     * @param clientId Optional client identifier (from header or other source)
     * @return Map of grouped weather stations
     */
    public Map<String, List<WeatherStation>> groupWeatherStations(List<WeatherStation> stations, String clientId) {
        WeatherFilteringStrategy strategy = determineStrategy(clientId);
        
        if (strategy instanceof WorkgroupBasedWeatherFilteringStrategy && clientId != null) {
            // For workgroup-based filtering, we need to get the workgroup name from config
            WeatherFilteringProperties.WorkgroupConfig workgroupConfig = 
                weatherFilteringProperties.workgroupClients().get(clientId);
            
            if (workgroupConfig != null) {
                log.debug("Using workgroup-based filtering for client: {} with workgroup: {}", 
                         clientId, workgroupConfig.workgroupName());
                
                // Filter stations by workgroup but still group by region for response structure
                List<WeatherStation> filteredStations = workgroupBasedStrategy
                    .filterStations(stations, workgroupConfig.workgroupName());
                
                return filteredStations.stream()
                    .collect(Collectors.groupingBy(WeatherStation::getRegion));
            }
        }
        
        // Default region-based grouping
        log.debug("Using region-based filtering for client: {}", clientId);
        return stations.stream()
            .collect(Collectors.groupingBy(WeatherStation::getRegion));
    }
    
    /**
     * Filters weather stations for a specific region/workgroup based on the client configuration.
     * 
     * @param stations List of weather stations to filter
     * @param filterValue The region or workgroup to filter by
     * @param clientId Optional client identifier
     * @return Filtered list of weather stations
     */
    public List<WeatherStation> filterStationsForRegion(List<WeatherStation> stations, 
                                                       String filterValue, 
                                                       String clientId) {
        WeatherFilteringStrategy strategy = determineStrategy(clientId);
        
        if (strategy instanceof WorkgroupBasedWeatherFilteringStrategy && clientId != null) {
            WeatherFilteringProperties.WorkgroupConfig workgroupConfig = 
                weatherFilteringProperties.workgroupClients().get(clientId);
            
            if (workgroupConfig != null) {
                // For workgroup-based clients, filter by workgroup but only return stations 
                // that also match the requested region
                return workgroupBasedStrategy.filterStations(stations, workgroupConfig.workgroupName())
                    .stream()
                    .filter(station -> station.getRegion().equals(filterValue) || 
                                     filterValue.equals("System"))
                    .toList();
            }
        }
        
        // Default region-based filtering
        return regionBasedStrategy.filterStations(stations, filterValue);
    }
    
    private WeatherFilteringStrategy determineStrategy(String clientId) {
        if (clientId != null && weatherFilteringProperties.workgroupClients().containsKey(clientId)) {
            return workgroupBasedStrategy;
        }
        return regionBasedStrategy;
    }
}
