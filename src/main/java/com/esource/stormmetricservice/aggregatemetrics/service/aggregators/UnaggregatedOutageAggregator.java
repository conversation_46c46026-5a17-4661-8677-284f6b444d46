package com.esource.stormmetricservice.aggregatemetrics.service.aggregators;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.etr.exceptions.NoOutageFoundException;
import com.esource.stormmetricservice.etr.model.UnaggregatedOutage;
import com.esource.stormmetricservice.etr.repository.UnaggregatedOutageRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEvent;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@ConditionalOnProperty(
        prefix = "metrics.aggregator.cumulative-outage",
        name = "strategy",
        havingValue = "unaggregated-outages")
@Component
public class UnaggregatedOutageAggregator implements OutageAggregator {

    private static final String UNAGGREGATED_OUTAGE_ID = "unaggregatedOutageId";

    private final UnaggregatedOutageRepository unaggregatedOutageRepository;
    private String[] regions;
    private int lookBackMinutes;

    public UnaggregatedOutageAggregator(
            UnaggregatedOutageRepository unaggregatedOutageRepository,
            @Value(
                            "${metrics.unaggregated-outage.regions:Anniston,Montgomery,Tuscaloosa,Mobile,Birmingham,System,Eufaula}")
                    String[] regions,
            @Value("${metrics.aggregator.look-back-minutes:2}") int lookBackMinutes) {
        this.unaggregatedOutageRepository = unaggregatedOutageRepository;
        this.regions = regions;
        this.lookBackMinutes = lookBackMinutes;
    }

    @Override
    public Map<String, Outage> aggregateOutages(AggregateOverviewEvent aggregateOverviewEvent) {
        ZonedDateTime timestamp = getTimestamp(aggregateOverviewEvent);
        if (!aggregateOverviewEvent.getMetadata().containsKey(UNAGGREGATED_OUTAGE_ID)) {
            log.error("No unaggregatedOutageId found for timestamp: {}", timestamp);
            return new HashMap<>();
        }

        UnaggregatedOutage currentOutage = getOutageByEventOrMostRecent(aggregateOverviewEvent, timestamp);
        ZonedDateTime lookBackThreshold = currentOutage.getCreatedTimestamp().minusMinutes(lookBackMinutes);
        UnaggregatedOutage previousOutage = getPreviousOutage(lookBackThreshold);

        Map<String, Outage> outagesByRegion = aggregateUnaggregatedOutageData(currentOutage, previousOutage);

        Map<String, Outage> zeroOutages = initializeZeroOutages();
        zeroOutages.forEach(outagesByRegion::putIfAbsent);
        return outagesByRegion;
    }

    private ZonedDateTime getTimestamp(AggregateOverviewEvent event) {
        return Optional.ofNullable(event.getTimestamp()).orElseGet(ZonedDateTime::now);
    }

    private UnaggregatedOutage getOutageByEventOrMostRecent(AggregateOverviewEvent event, ZonedDateTime timestamp) {
        ObjectId unaggregatedOutageId = Optional.ofNullable(event.getMetadata())
                .map(metadata -> metadata.get(UNAGGREGATED_OUTAGE_ID))
                .map(Object::toString)
                .map(ObjectId::new)
                .orElse(null);

        return Optional.ofNullable(unaggregatedOutageId)
                .flatMap(unaggregatedOutageRepository::findById)
                .orElseGet(() -> unaggregatedOutageRepository
                        .findFirstByCreatedTimestampLessThanEqualOrderByCreatedTimestampDesc(timestamp)
                        .orElseThrow(NoOutageFoundException::new));
    }

    private UnaggregatedOutage getPreviousOutage(ZonedDateTime lookBackThreshold) {
        return unaggregatedOutageRepository
                .findFirstByCreatedTimestampLessThanOrderByCreatedTimestampDesc(lookBackThreshold)
                .orElse(null);
    }

    private Map<String, Outage> aggregateUnaggregatedOutageData(
            UnaggregatedOutage currentOutage, UnaggregatedOutage previousOutage) {
        Map<String, Outage> outagesByRegion = new HashMap<>();
        Map<String, Set<String>> uniqueIncidentsPerRegion = new HashMap<>();

        // Process current outage data
        if (currentOutage != null) {
            for (UnaggregatedOutage.RawOutage rawOutage : currentOutage.getRawOutages()) {
                String region = rawOutage.getRegion();
                String incidentName = rawOutage.getIncidentName();
                int affectedCustomers = rawOutage.getAffectedCustomers();

                uniqueIncidentsPerRegion.putIfAbsent(region, new HashSet<>());
                boolean isUniqueIncident = uniqueIncidentsPerRegion.get(region).add(incidentName);

                Outage outage = outagesByRegion.computeIfAbsent(region, k -> Outage.builder()
                        .totalIncidents(0)
                        .totalAffectedCustomers(0)
                        .uniqueIncidentsCount(0)
                        .uniqueAffectedCustomers(0)
                        .outageIncidentMap(new HashMap<>())
                        .build());

                outage.setTotalIncidents(outage.getTotalIncidents() + 1);
                outage.setTotalAffectedCustomers(outage.getTotalAffectedCustomers() + affectedCustomers);
                outage.getOutageIncidentMap().put(incidentName, affectedCustomers);

                if (isUniqueIncident) {
                    outage.setUniqueIncidentsCount(outage.getUniqueIncidentsCount() + 1);
                    outage.setUniqueAffectedCustomers(outage.getUniqueAffectedCustomers() + affectedCustomers);
                }
            }
        }

        // Process previous outage data
        if (previousOutage != null) {
            for (UnaggregatedOutage.RawOutage rawOutage : previousOutage.getRawOutages()) {
                String region = rawOutage.getRegion();
                String incidentName = rawOutage.getIncidentName();
                int affectedCustomers = rawOutage.getAffectedCustomers();

                if (uniqueIncidentsPerRegion
                        .getOrDefault(region, new HashSet<>())
                        .contains(incidentName)) {
                    Outage outage = outagesByRegion.get(region);
                    outage.setUniqueIncidentsCount(outage.getUniqueIncidentsCount() - 1);
                    outage.setUniqueAffectedCustomers(outage.getUniqueAffectedCustomers() - affectedCustomers);
                }
            }
        }

        // Calculate "System" region totals
        calculateSystemTotals(outagesByRegion);

        return outagesByRegion;
    }

    private void calculateSystemTotals(Map<String, Outage> outagesByRegion) {
        Outage systemOutage = Outage.builder()
                .totalAffectedCustomers(0)
                .totalIncidents(0)
                .uniqueAffectedCustomers(0)
                .uniqueIncidentsCount(0)
                .outageIncidentMap(new HashMap<>())
                .build();

        for (Map.Entry<String, Outage> entry : outagesByRegion.entrySet()) {
            if (!MetricConstants.SYSTEM_REGION.equals(entry.getKey())) {
                Outage regionalOutage = entry.getValue();

                systemOutage.setTotalIncidents(systemOutage.getTotalIncidents() + regionalOutage.getTotalIncidents());
                systemOutage.setTotalAffectedCustomers(
                        systemOutage.getTotalAffectedCustomers() + regionalOutage.getTotalAffectedCustomers());
                systemOutage.setUniqueIncidentsCount(
                        systemOutage.getUniqueIncidentsCount() + regionalOutage.getUniqueIncidentsCount());
                systemOutage.setUniqueAffectedCustomers(
                        systemOutage.getUniqueAffectedCustomers() + regionalOutage.getUniqueAffectedCustomers());

                regionalOutage.getOutageIncidentMap().forEach((incidentId, affectedCustomers) -> systemOutage
                        .getOutageIncidentMap()
                        .merge(incidentId, affectedCustomers, Math::max));
            }
        }

        outagesByRegion.put(MetricConstants.SYSTEM_REGION, systemOutage);
    }

    private Map<String, Outage> initializeZeroOutages() {
        Map<String, Outage> zeroOutages = new HashMap<>();
        Arrays.stream(regions)
                .forEach(region -> zeroOutages.put(
                        region,
                        Outage.builder()
                                .totalIncidents(0)
                                .totalAffectedCustomers(0)
                                .uniqueIncidentsCount(0)
                                .uniqueAffectedCustomers(0)
                                .outageIncidentMap(new HashMap<>())
                                .build()));
        return zeroOutages;
    }
}
