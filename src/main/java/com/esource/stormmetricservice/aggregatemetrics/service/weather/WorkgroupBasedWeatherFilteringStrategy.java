package com.esource.stormmetricservice.aggregatemetrics.service.weather;

import java.util.List;

import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.weather.model.RawWeatherData.WeatherStation;

import static com.esource.stormmetricservice.common.util.MetricConstants.SYSTEM_REGION;

/**
 * Weather filtering strategy that filters weather stations by workgroup.
 * Used for specific clients that require workgroup-based weather data filtering.
 */
@Component
public class WorkgroupBasedWeatherFilteringStrategy implements WeatherFilteringStrategy {
    
    @Override
    public List<WeatherStation> filterStations(List<WeatherStation> stations, String workgroup) {
        return stations.stream()
                .filter(station -> {
                    if (workgroup.equals(SYSTEM_REGION)) {
                        return true; // System region includes all stations
                    }
                    // Handle case-insensitive matching and normalize spaces
                    String stationWorkgroup = normalizeWorkgroup(station.getWorkGroup());
                    String targetWorkgroup = normalizeWorkgroup(workgroup);
                    return stationWorkgroup.equals(targetWorkgroup);
                })
                .toList();
    }
    
    @Override
    public String getGroupingKey(WeatherStation station) {
        // For workgroup-based filtering, we still group by region for the response structure
        // but the filtering is done by workgroup
        return station.getRegion();
    }
    
    /**
     * Normalizes workgroup names for comparison.
     * Converts "MERIDIAN ZONE" to "Meridian Zone" format.
     */
    private String normalizeWorkgroup(String workgroup) {
        if (workgroup == null) {
            return "";
        }
        // Convert to title case: "MERIDIAN ZONE" -> "Meridian Zone"
        return workgroup.toLowerCase()
                .replaceAll("\\b\\w", match -> match.toUpperCase());
    }
}
