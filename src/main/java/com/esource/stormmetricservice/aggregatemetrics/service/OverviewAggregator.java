package com.esource.stormmetricservice.aggregatemetrics.service;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.aggregatemetrics.model.Overview;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Resource;
import com.esource.stormmetricservice.aggregatemetrics.repository.OverviewRepository;
import com.esource.stormmetricservice.aggregatemetrics.service.aggregators.OutageAggregator;
import com.esource.stormmetricservice.aggregatemetrics.service.aggregators.ResourceAggregator;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEvent;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@AllArgsConstructor
public class OverviewAggregator {

    private OutageAggregator outageAggregator;
    private final ResourceAggregator resourceAggregator;

    private final OverviewRepository overviewMetricRepository;

    public void goCollectMetrics(AggregateOverviewEvent aggregateOverviewEvent) {
        Map<String, Outage> outages = outageAggregator.aggregateOutages(aggregateOverviewEvent);
        Map<String, Resource> resources = resourceAggregator.aggregateResources(aggregateOverviewEvent);

        ZonedDateTime timestamp = aggregateOverviewEvent.getTimestamp().truncatedTo(ChronoUnit.MINUTES);

        Overview overview = Overview.builder()
                .timestamp(timestamp)
                .data(Overview.OverviewData.builder()
                        .outages(outages)
                        .resources(resources)
                        .build())
                .build();

        Optional<Overview> existingOverview = overviewMetricRepository.findByTimestamp(timestamp);

        if (existingOverview.isPresent()) {
            log.debug("Overview already exists for timestamp: {}", timestamp);
            overviewMetricRepository.deleteByTimestamp(timestamp);
            overviewMetricRepository.save(overview);
            log.debug("Overview updated successfully for timestamp: {}", timestamp);
        } else {
            overviewMetricRepository.save(overview);
            log.debug("New overview saved successfully for timestamp: {}", timestamp);
        }
    }
}
