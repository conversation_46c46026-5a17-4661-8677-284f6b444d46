package com.esource.stormmetricservice.aggregatemetrics.service.aggregators;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.aggregatemetrics.model.dto.Resource;
import com.esource.stormmetricservice.etr.model.ResourceEntity;
import com.esource.stormmetricservice.etr.model.StormRecord;
import com.esource.stormmetricservice.etr.model.dto.AggregatedResourcesDTO;
import com.esource.stormmetricservice.etr.repository.StormRecordRepository;
import com.esource.stormmetricservice.etr.service.ResourceService;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEvent;

import lombok.AllArgsConstructor;

import static java.time.ZoneOffset.UTC;

@Service
@AllArgsConstructor
public class ResourceAggregator {

    private final ResourceService resourceService;

    private final StormRecordRepository stormRecordRepository;

    public Map<String, Resource> aggregateResources(AggregateOverviewEvent build) {
        List<StormRecord> records = stormRecordRepository.findActiveNonArchivedStorms(ZonedDateTime.now(UTC));
        String stormId = records.stream().findFirst().map(StormRecord::getId).orElse(null);
        List<AggregatedResourcesDTO> resourcesDTOS = resourceService.getCurrentResources(
                stormId, build.getTimestamp(), ResourceEntity.ResourceConstants.GENERATION_TYPE_MANUAL, null);

        // Calculate the total of all resources and group resources by key
        int totalWorkers = resourcesDTOS.stream()
                .flatMap(dto -> dto.getTotalResources().values().stream())
                .reduce(0, Integer::sum);

        Map<String, Integer> groupedResources = resourcesDTOS.stream()
                .map(AggregatedResourcesDTO::getTotalResources)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, Integer::sum));

        Map<String, Resource> resources = resourcesDTOS.stream()
                .collect(Collectors.toMap(AggregatedResourcesDTO::getTerritoryName, dto -> Resource.builder()
                        .totalResources(
                                dto.getTotalResources().values().stream().reduce(0, Integer::sum))
                        .resources(dto.getTotalResources())
                        .build()));

        // Add the system total entry
        resources.put(
                "System",
                Resource.builder()
                        .totalResources(totalWorkers)
                        .resources(groupedResources)
                        .build());
        return resources;
    }
}
