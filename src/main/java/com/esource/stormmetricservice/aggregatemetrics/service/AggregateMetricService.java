package com.esource.stormmetricservice.aggregatemetrics.service;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.OptionalDouble;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric;
import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric.OverviewMetrics;
import com.esource.stormmetricservice.aggregatemetrics.model.Overview;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Resource;
import com.esource.stormmetricservice.aggregatemetrics.model.dto.Weather;
import com.esource.stormmetricservice.aggregatemetrics.repository.OverviewRepository;
import com.esource.stormmetricservice.weather.model.RawWeatherData.WeatherStation;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.esource.stormmetricservice.common.util.MetricConstants.SYSTEM_REGION;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.BooleanUtils.isFalse;

@Slf4j
@RequiredArgsConstructor
@Service
public class AggregateMetricService {
    private final OverviewRepository overviewRepository;
    private final ObjectMapper objectMapper;

    @Value("${weather.aggregate.regionalFilter:region}")
    private String regionalFilter;

    private static final double[] WIND_GUST_LEVELS = {20, 30, 40};

    public AggregateMetric getAggregateMetrics(
            ZonedDateTime start, ZonedDateTime end, ZonedDateTime accumulationStart, ZonedDateTime accumulationEnd) {
        return buildOverviewMetrics(start, end, accumulationStart, accumulationEnd);
    }

    public AggregateMetric filterAggregateMetrics(AggregateMetric aggregateMetrics, List<String> regionsToFilter) {
        if (regionsToFilter == null || regionsToFilter.isEmpty()) {
            return aggregateMetrics;
        }

        List<String> filteredRegions = regionsToFilter.stream()
                .filter(region -> !region.trim().isEmpty())
                .distinct()
                .toList();
        Map<String, List<OverviewMetrics>> filteredRegionalMetrics = new HashMap<>();
        filteredRegions.forEach(region -> {
            if (aggregateMetrics.getRegionalMetrics().get(region) != null) {
                filteredRegionalMetrics.put(
                        region, aggregateMetrics.getRegionalMetrics().get(region));
            }
        });
        return AggregateMetric.builder()
                .regionalMetrics(filteredRegionalMetrics)
                .startTime(aggregateMetrics.getStartTime())
                .endTime(aggregateMetrics.getEndTime())
                .build();
    }

    private AggregateMetric buildOverviewMetrics(
            ZonedDateTime start, ZonedDateTime end, ZonedDateTime accumulationStart, ZonedDateTime accumulationEnd) {

        ZonedDateTime startDate = start.minusSeconds(1);
        ZonedDateTime endDate = end.plusSeconds(1);
        List<Overview> overviews = overviewRepository.findByTimestampBetweenWithWeather(
                startDate, endDate, startDate.truncatedTo(ChronoUnit.HOURS), endDate.truncatedTo(ChronoUnit.HOURS));

        Map<String, List<OverviewMetrics>> regionalMetrics = buildRegionalMetrics(overviews);

        regionalMetrics
                .values()
                .forEach(metrics -> metrics.sort(Comparator.comparing(
                        OverviewMetrics::getTimestamp, Comparator.nullsLast(Comparator.naturalOrder()))));

        return AggregateMetric.builder()
                .startTime(start)
                .endTime(end)
                .regionalMetrics(calculateCumulativeValues(regionalMetrics, accumulationStart, accumulationEnd))
                .build();
    }

    private Map<String, List<OverviewMetrics>> buildRegionalMetrics(List<Overview> overviews) {
        Map<String, List<OverviewMetrics>> regionalMetrics = new HashMap<>();
        Set<String> allRegions = new HashSet<>();

        for (Overview overview : overviews) {
            Overview.OverviewData data = overview.getData();
            if (data == null) {
                return new HashMap<>();
            }
            Map<String, Resource> resources = data.getResources();

            List<WeatherStation> stations = convertStationData(data.getStationData());
            stations.forEach(station -> allRegions.add(getGroupingKey(station)));

            Map<String, Weather> weathers =
                    stations.stream().collect(Collectors.groupingBy(this::getGroupingKey)).entrySet().stream()
                            .collect(Collectors.toMap(Map.Entry::getKey, entry -> Weather.builder()
                                    .regionalWeather(entry.getValue())
                                    .build()));
            weathers.putIfAbsent(
                    SYSTEM_REGION, Weather.builder().regionalWeather(stations).build());

            Map<String, Outage> outages = new HashMap<>(data.getOutages());
            allRegions.forEach(region -> outages.putIfAbsent(region, createZeroOutage()));

            outages.forEach((region, outage) -> {
                Resource resource = resources.getOrDefault(
                        region,
                        Resource.builder().totalResources(0).resources(Map.of()).build());

                Weather weather =
                        weathers.getOrDefault(region, Weather.builder().build());

                Double maxWindGustMph = calculateMaxWindGustMph(weather, region);
                Double averageWindSpeedMph = calculateAverageWindSpeedMph(weather, region);

                List<OverviewMetrics> metricsList = regionalMetrics.getOrDefault(region, new ArrayList<>());

                metricsList.add(OverviewMetrics.builder()
                        .timestamp(overview.getTimestamp())
                        .totalIncidents(outage.getTotalIncidents())
                        .totalAffectedCustomers(outage.getTotalAffectedCustomers())
                        .totalRawIncidents(outage.getTotalRawIncidents())
                        .totalRawAffectedCustomers(outage.getTotalRawAffectedCustomers())
                        .uniqueIncidentsCount(outage.getUniqueIncidentsCount())
                        .uniqueAffectedCustomers(outage.getUniqueAffectedCustomers())
                        .totalResources(resource.getTotalResources())
                        .resources(resource.getResources())
                        .averageWindSpeed(averageWindSpeedMph)
                        .windGustMph(maxWindGustMph)
                        .outageIncidentMap(outage.getOutageIncidentMap())
                        .build());

                regionalMetrics.put(region, metricsList);
            });
        }
        return regionalMetrics;
    }

    private Outage createZeroOutage() {
        return Outage.builder()
                .totalIncidents(0)
                .totalAffectedCustomers(0)
                .uniqueIncidentsCount(0)
                .uniqueAffectedCustomers(0)
                .build();
    }

    private Map<String, List<OverviewMetrics>> calculateCumulativeValues(
            Map<String, List<OverviewMetrics>> regionalMetrics,
            ZonedDateTime accumulationStart,
            ZonedDateTime accumulationEnd) {

        Map<String, List<OverviewMetrics>> newRegionalMetrics = new HashMap<>();

        for (Map.Entry<String, List<OverviewMetrics>> entry : regionalMetrics.entrySet()) {
            String region = entry.getKey();
            List<OverviewMetrics> originalMetrics = entry.getValue();
            List<OverviewMetrics> updatedMetrics = new ArrayList<>();

            CumulativeAccumulators accumulators = initAccumulators(originalMetrics);

            for (int i = 0; i < originalMetrics.size(); i++) {
                OverviewMetrics current = originalMetrics.get(i);
                OverviewMetrics previous = (i > 0) ? originalMetrics.get(i - 1) : null;

                boolean isWithinStormWindow = isWithinStormWindow(current, accumulationStart, accumulationEnd);

                if (hasOutageData(current)) {
                    handleMetricsWithOutage(current, previous, accumulators, isWithinStormWindow, accumulationStart);
                } else {
                    handleMetricsWithoutOutage(
                            current, previous, accumulators, isWithinStormWindow, accumulationStart, i);
                }

                updatedMetrics.add(current);
            }
            newRegionalMetrics.put(region, updatedMetrics);
        }

        return newRegionalMetrics;
    }

    private static class CumulativeAccumulators {
        int cumulativeAffectedCustomers;
        int cumulativeIncidents;
        int cumulativeRestorations;
        int cumulativeCustomersRestored;

        Map<String, Integer> cumulativeWindGustThresholds;

        Map<String, Integer> activeIncidents;

        Set<String> allUniqueIncidents;
        Map<String, Integer> allUniqueAffectedCustomers;

        Set<String> allRestoredIncidents;
        Map<String, Integer> allCustomersRestored;
    }

    /**
     * Initializes our counters and sets for a region's entire metric list.
     */
    private CumulativeAccumulators initAccumulators(List<OverviewMetrics> metrics) {
        CumulativeAccumulators accumulators = new CumulativeAccumulators();

        accumulators.cumulativeAffectedCustomers =
                metrics.isEmpty() ? 0 : metrics.get(0).getTotalAffectedCustomers();
        accumulators.cumulativeIncidents =
                metrics.isEmpty() ? 0 : metrics.get(0).getTotalIncidents();
        accumulators.cumulativeRestorations = 0;
        accumulators.cumulativeCustomersRestored = 0;

        accumulators.cumulativeWindGustThresholds = initializeWindGustThresholds();

        accumulators.activeIncidents = new HashMap<>();
        if (!metrics.isEmpty() && metrics.get(0).getOutageIncidentMap() != null) {
            accumulators.activeIncidents.putAll(metrics.get(0).getOutageIncidentMap());
        }

        accumulators.allUniqueIncidents = new HashSet<>(accumulators.activeIncidents.keySet());
        accumulators.allUniqueAffectedCustomers = new HashMap<>(accumulators.activeIncidents);

        accumulators.allRestoredIncidents = new HashSet<>();
        accumulators.allCustomersRestored = new HashMap<>();

        return accumulators;
    }

    /**
     * Main handler for when {@link OverviewMetrics} has outage-incident data.
     */
    private void handleMetricsWithOutage(
            OverviewMetrics current,
            OverviewMetrics previous,
            CumulativeAccumulators accumulators,
            boolean isWithinStormWindow,
            ZonedDateTime accumulationStart) {

        Map<String, Integer> currentIncidents = current.getOutageIncidentMap();
        Map<String, Integer> previousIncidents = (previous != null && previous.getOutageIncidentMap() != null)
                ? previous.getOutageIncidentMap()
                : Collections.emptyMap();

        Set<String> restoredIncidentIds = findRestoredIncidents(previousIncidents, currentIncidents);
        Map<String, Integer> newIncidents = calculateNewIncidents(previousIncidents, currentIncidents);

        int totalRestorations = restoredIncidentIds.size();
        int totalAffectedCustomerRestorations =
                restoredIncidentIds.stream().mapToInt(previousIncidents::get).sum();

        if (isWithinStormWindow) {
            accumulators.activeIncidents.keySet().removeAll(restoredIncidentIds);
            accumulators.activeIncidents.putAll(newIncidents);

            accumulators.allUniqueIncidents.addAll(newIncidents.keySet());
            newIncidents.forEach(accumulators.allUniqueAffectedCustomers::putIfAbsent);

            accumulators.allRestoredIncidents.addAll(restoredIncidentIds);
            restoredIncidentIds.forEach(
                    id -> accumulators.allCustomersRestored.putIfAbsent(id, previousIncidents.get(id)));
        }

        current.setTotalIncidents(currentIncidents.size());
        current.setTotalAffectedCustomers(
                currentIncidents.values().stream().mapToInt(Integer::intValue).sum());
        current.setUniqueIncidentsCount(newIncidents.size());
        current.setUniqueAffectedCustomers(
                newIncidents.values().stream().mapToInt(Integer::intValue).sum());
        current.setRestoredPerWorker(calculateRestoredPerWorker(totalRestorations, current.getTotalResources()));

        if (didHourChange(previous, current)) {
            updateWindGustThresholds(accumulators.cumulativeWindGustThresholds, current.getWindGustMph());
        }

        accumulators.cumulativeIncidents = accumulators.allUniqueIncidents.size();
        accumulators.cumulativeAffectedCustomers = accumulators.allUniqueAffectedCustomers.values().stream()
                .mapToInt(Integer::intValue)
                .sum();
        accumulators.cumulativeRestorations = accumulators.allRestoredIncidents.size();
        accumulators.cumulativeCustomersRestored = accumulators.allCustomersRestored.values().stream()
                .mapToInt(Integer::intValue)
                .sum();

        updateMetricsData(
                current,
                totalRestorations,
                totalAffectedCustomerRestorations,
                isWithinOrAfterStormWindow(current, accumulationStart) ? accumulators.cumulativeAffectedCustomers : 0,
                isWithinOrAfterStormWindow(current, accumulationStart) ? accumulators.cumulativeIncidents : 0,
                isWithinOrAfterStormWindow(current, accumulationStart) ? accumulators.cumulativeRestorations : 0,
                isWithinOrAfterStormWindow(current, accumulationStart) ? accumulators.cumulativeCustomersRestored : 0,
                accumulators.cumulativeWindGustThresholds,
                new HashMap<>(accumulators.activeIncidents));
    }

    /**
     * Handler for when {@link OverviewMetrics} has no outage-incident data.
     */
    private void handleMetricsWithoutOutage(
            OverviewMetrics current,
            OverviewMetrics previous,
            CumulativeAccumulators accumulators,
            boolean isWithinStormWindow,
            ZonedDateTime accumulationStart,
            int currentIndex) {

        int totalRestorations = calculateTotalRestorations(current, previous);
        int totalAffectedCustomerRestorations = calculateTotalAffectedCustomerRestorations(current, previous);
        current.setRestoredPerWorker(calculateRestoredPerWorker(totalRestorations, current.getTotalResources()));

        if (isWithinStormWindow) {

            if (currentIndex > 0) {
                accumulators.cumulativeAffectedCustomers += current.getUniqueAffectedCustomers();
                accumulators.cumulativeIncidents += current.getUniqueIncidentsCount();
            }
            accumulators.cumulativeRestorations += totalRestorations;
            accumulators.cumulativeCustomersRestored += totalAffectedCustomerRestorations;
        }

        if (didHourChange(previous, current)) {
            updateWindGustThresholds(accumulators.cumulativeWindGustThresholds, current.getWindGustMph());
        }

        updateMetricsData(
                current,
                totalRestorations,
                totalAffectedCustomerRestorations,
                isWithinOrAfterStormWindow(current, accumulationStart) ? accumulators.cumulativeAffectedCustomers : 0,
                isWithinOrAfterStormWindow(current, accumulationStart) ? accumulators.cumulativeIncidents : 0,
                isWithinOrAfterStormWindow(current, accumulationStart) ? accumulators.cumulativeRestorations : 0,
                isWithinOrAfterStormWindow(current, accumulationStart) ? accumulators.cumulativeCustomersRestored : 0,
                accumulators.cumulativeWindGustThresholds,
                Collections.emptyMap());
    }

    private boolean hasOutageData(OverviewMetrics current) {
        return current.getOutageIncidentMap() != null
                && !current.getOutageIncidentMap().isEmpty();
    }

    private Set<String> findRestoredIncidents(
            Map<String, Integer> previousIncidents, Map<String, Integer> currentIncidents) {
        return previousIncidents.keySet().stream()
                .filter(id -> !currentIncidents.containsKey(id))
                .collect(Collectors.toSet());
    }

    private boolean didHourChange(OverviewMetrics previous, OverviewMetrics current) {
        return previous == null
                || previous.getTimestamp().getHour() != current.getTimestamp().getHour();
    }

    private boolean isWithinStormWindow(
            OverviewMetrics current, ZonedDateTime accumulationStart, ZonedDateTime accumulationEnd) {
        return (accumulationStart == null || !current.getTimestamp().isBefore(accumulationStart))
                && (accumulationEnd == null || !current.getTimestamp().isAfter(accumulationEnd));
    }

    private boolean isWithinOrAfterStormWindow(OverviewMetrics current, ZonedDateTime accumulationStart) {
        return accumulationStart == null || !current.getTimestamp().isBefore(accumulationStart);
    }

    private Map<String, Integer> calculateNewIncidents(
            Map<String, Integer> previousIncidents, Map<String, Integer> currentIncidents) {
        return currentIncidents.entrySet().stream()
                .filter(entry -> !previousIncidents.containsKey(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private int calculateTotalRestorations(OverviewMetrics current, OverviewMetrics previous) {
        return (previous != null) ? Math.max(0, previous.getTotalIncidents() - current.getTotalIncidents()) : 0;
    }

    private int calculateTotalAffectedCustomerRestorations(OverviewMetrics current, OverviewMetrics previous) {
        return (previous != null)
                ? Math.max(0, previous.getTotalAffectedCustomers() - current.getTotalAffectedCustomers())
                : 0;
    }

    private Map<String, Integer> initializeWindGustThresholds() {
        Map<String, Integer> thresholds = new HashMap<>();
        for (double level : WIND_GUST_LEVELS) {
            thresholds.put("over" + (int) level + "mph", 0);
        }
        return thresholds;
    }

    private void updateWindGustThresholds(Map<String, Integer> thresholds, double windGustMph) {
        thresholds.forEach((key, value) -> {
            double level = Double.parseDouble(key.substring(4, key.length() - 3));
            if (windGustMph > level) {
                thresholds.put(key, value + 1);
            }
        });
    }

    private double calculateRestoredPerWorker(int totalRestorations, int totalResources) {
        return (totalRestorations > 0 && totalResources > 0) ? (double) totalRestorations / totalResources : 0.0;
    }

    private void updateMetricsData(
            OverviewMetrics metrics,
            int totalRestorations,
            int totalAffectedCustomerRestorations,
            int cumulativeAffectedCustomers,
            int cumulativeIncidents,
            int cumulativeRestorations,
            int cumulativeCustomersRestored,
            Map<String, Integer> cumulativeWindGustThresholds,
            Map<String, Integer> outageIncidentMap) {

        metrics.setTotalRestorations(totalRestorations);
        metrics.setTotalAffectedCustomerRestorations(totalAffectedCustomerRestorations);
        metrics.setCumulativeAffectedCustomers(cumulativeAffectedCustomers);
        metrics.setCumulativeIncidents(cumulativeIncidents);
        metrics.setCumulativeRestorations(cumulativeRestorations);
        metrics.setCumulativeCustomersRestored(cumulativeCustomersRestored);
        metrics.setWindGustThresholds(new HashMap<>(cumulativeWindGustThresholds));
        metrics.setOutageIncidentMap(new HashMap<>(outageIncidentMap));
    }

    private Double calculateAverageWindSpeedMph(Weather weather, String region) {
        if (weather == null || weather.getRegionalWeather() == null) {
            return 0.0;
        }

        List<WeatherStation> stations = weather.getRegionalWeather().stream()
                .filter(station -> (matchesRegionOrWorkgroup(station, region) || region.equals(SYSTEM_REGION)))
                .toList();

        OptionalDouble average = stations.stream()
                .mapToDouble(station -> station.getWeatherAttributes().getWindSpeedMph())
                .average();
        return average.isPresent() ? average.getAsDouble() : 0.0;
    }

    private Double calculateMaxWindGustMph(Weather weather, String region) {
        if (weather == null || weather.getRegionalWeather() == null) {
            return 0.0;
        }

        List<WeatherStation> stations = weather.getRegionalWeather().stream()
                .filter(station -> (matchesRegionOrWorkgroup(station, region) || region.equals(SYSTEM_REGION)))
                .toList();

        return stations.stream()
                .mapToDouble(station -> station.getWeatherAttributes().getWindGustMph())
                .max()
                .orElse(0.0);
    }

    public AggregateMetric aggregateMetricsByGranularity(AggregateMetric filteredMetrics, Integer outageGranularity) {
        Map<String, List<OverviewMetrics>> aggregatedByOutageGranularity = new HashMap<>();
        filteredMetrics.getRegionalMetrics().forEach((region, metrics) -> {
            if (metrics != null) {
                List<OverviewMetrics> minMetrics = getMinOutagesByGranularity(metrics, outageGranularity);
                aggregatedByOutageGranularity.put(region, minMetrics);
            } else {
                aggregatedByOutageGranularity.put(region, Collections.emptyList());
            }
        });
        return AggregateMetric.builder()
                .regionalMetrics(aggregatedByOutageGranularity)
                .endTime(filteredMetrics.getEndTime())
                .startTime(filteredMetrics.getStartTime())
                .build();
    }

    protected List<OverviewMetrics> getMinOutagesByGranularity(List<OverviewMetrics> outages, int outageGranularity) {
        TreeMap<ZonedDateTime, List<OverviewMetrics>> outagesGroupedByTime = outages.stream()
                .collect(Collectors.groupingBy(
                        outage -> roundToGranularity(outage.getTimestamp(), outageGranularity, ChronoUnit.MINUTES),
                        TreeMap::new,
                        Collectors.toList()));

        List<OverviewMetrics> minOutagesByGroup = outagesGroupedByTime.values().stream()
                .map(group -> group.stream()
                        .min(Comparator.comparing(OverviewMetrics::getTimestamp))
                        .orElseThrow(() -> new IllegalStateException("Unexpected state, group should not be empty")))
                .collect(Collectors.toList());

        if (isFalse(outagesGroupedByTime.isEmpty())) {
            ZonedDateTime lastGroupedTimestamp = outagesGroupedByTime.lastKey();
            List<OverviewMetrics> lastGroup = outagesGroupedByTime.get(lastGroupedTimestamp);

            if (nonNull(lastGroup) && lastGroup.size() > 1) {
                minOutagesByGroup.add(lastGroup.getLast());
            }
        }
        return minOutagesByGroup;
    }

    /**
     * Rounds a given ZonedDateTime to the nearest granularity.
     *
     * @param dateTime    the ZonedDateTime to be rounded
     * @param granularity the granularity to which to round
     * @param unit        the unit of time for granularity
     * @return            the rounded ZonedDateTime
     */
    protected ZonedDateTime roundToGranularity(ZonedDateTime dateTime, int granularity, ChronoUnit unit) {
        ZonedDateTime startOfDay = dateTime.truncatedTo(ChronoUnit.DAYS);
        long unitsSinceStartOfDay = unit.between(startOfDay, dateTime);
        long roundedUnits = (unitsSinceStartOfDay / granularity) * granularity;
        return startOfDay.plus(roundedUnits, unit);
    }

    private List<WeatherStation> convertStationData(List<Object> stationData) {
        if (stationData == null) {
            return Collections.emptyList();
        }
        return stationData.stream()
                .flatMap(item -> convertStationMapToWeatherStations((Map<String, Object>) item).stream())
                .toList();
    }

    private List<WeatherStation> convertStationMapToWeatherStations(Map<String, Object> stationMap) {
        List<WeatherStation> stations = new ArrayList<>();
        try {
            stationMap.remove("_id");

            for (Map.Entry<String, Object> entry : stationMap.entrySet()) {
                if (entry.getValue() instanceof Map) {
                    Map<String, Object> weatherStationMap = (Map<String, Object>) entry.getValue();
                    WeatherStation station = objectMapper.convertValue(weatherStationMap, WeatherStation.class);
                    if (station != null) {
                        stations.add(station);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error converting station map to weather stations", e);
        }
        return stations;
    }

    /**
     * Gets the appropriate grouping key for a weather station based on configuration.
     * Uses workgroup if regionalFilter is set to "workgroup", otherwise uses region.
     */
    private String getGroupingKey(WeatherStation station) {
        if ("workgroup".equalsIgnoreCase(regionalFilter)) {
            String workgroup = station.getWorkGroup();
            return workgroup != null ? workgroup : "";
        }
        return station.getRegion();
    }

    /**
     * Checks if a weather station matches the given region or workgroup based on configuration.
     */
    private boolean matchesRegionOrWorkgroup(WeatherStation station, String filterValue) {
        if ("workgroup".equalsIgnoreCase(regionalFilter)) {
            String workgroup = station.getWorkGroup();
            return workgroup != null && workgroup.equals(filterValue);
        }
        return station.getRegion().equals(filterValue);
    }
}
