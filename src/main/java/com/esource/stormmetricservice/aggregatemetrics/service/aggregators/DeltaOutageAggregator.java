package com.esource.stormmetricservice.aggregatemetrics.service.aggregators;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.aggregatemetrics.model.dto.Outage;
import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.etr.exceptions.NoOutageFoundException;
import com.esource.stormmetricservice.etr.model.ActiveOutages;
import com.esource.stormmetricservice.etr.repository.ActiveOutagesRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "metrics.aggregator.cumulative-outage", name = "strategy", havingValue = "delta")
@Component
public class DeltaOutageAggregator implements OutageAggregator {

    private final ActiveOutagesRepository activeOutagesRepository;

    @Value("${metrics.aggregator.look-back-minutes:2}")
    private int lookBackMinutes;

    private static final String ACTIVE_OUTAGE_ID = "activeOutageId";

    @Override
    public Map<String, Outage> aggregateOutages(AggregateOverviewEvent event) {
        ZonedDateTime timestamp = getTimestamp(event);
        if (!event.getMetadata().containsKey(ACTIVE_OUTAGE_ID)) {
            log.error("No activeOutageId found for timestamp: {}", timestamp);
            return new HashMap<>();
        }
        ActiveOutages currentOutage = getOutageByEventOrMostRecent(event, timestamp);
        ZonedDateTime lookBackThreshold = currentOutage.getCreatedTimestamp().minusMinutes(lookBackMinutes);

        ActiveOutages previousOutage = getPreviousOutage(lookBackThreshold);
        if (previousOutage == null || previousOutage.getId().equals(currentOutage.getId())) {
            log.error("No previous outage found for timestamp: {}", timestamp);
        }

        return aggregateOutageData(currentOutage, previousOutage);
    }

    private ZonedDateTime getTimestamp(AggregateOverviewEvent event) {
        return Optional.ofNullable(event.getTimestamp()).orElseGet(ZonedDateTime::now);
    }

    private ActiveOutages getOutageByEventOrMostRecent(AggregateOverviewEvent event, ZonedDateTime timestamp) {
        String activeOutageId = Optional.ofNullable(event.getMetadata())
                .map(metadata -> metadata.get(ACTIVE_OUTAGE_ID))
                .map(Object::toString)
                .orElse(null);

        return Optional.ofNullable(activeOutageId)
                .flatMap(activeOutagesRepository::findById)
                .orElseGet(() -> activeOutagesRepository
                        .findFirstByCreatedTimestampLessThanEqualOrderByCreatedTimestampDesc(timestamp.plusMinutes(1))
                        .orElseThrow(NoOutageFoundException::new));
    }

    private ActiveOutages getPreviousOutage(ZonedDateTime lookBackThreshold) {
        return activeOutagesRepository
                .findFirstByCreatedTimestampLessThanOrderByCreatedTimestampDesc(lookBackThreshold)
                .orElse(null);
    }

    private Map<String, Outage> aggregateOutageData(ActiveOutages currentOutage, ActiveOutages previousOutage) {
        Map<String, Outage> outagesByRegion = new HashMap<>();

        currentOutage.getOutageRecords().forEach(currentRecord -> {
            if (!MetricConstants.SYSTEM_REGION.equals(currentRecord.getRegion())) {
                Outage outage = previousOutage == null
                        ? createInitialOutage(currentRecord)
                        : compareAndBuildOutage(
                                currentRecord, findPreviousRecordByRegion(previousOutage, currentRecord.getRegion()));

                outagesByRegion.put(currentRecord.getRegion(), outage);
            }
        });

        calculateSystemTotals(outagesByRegion);

        return outagesByRegion;
    }

    private Outage createInitialOutage(ActiveOutages.OutageRecord currentRecord) {
        return Outage.builder()
                .totalIncidents(currentRecord.getActiveIncidents())
                .totalAffectedCustomers(currentRecord.getActiveCustomerOutages())
                .totalRawIncidents(currentRecord.getRawActiveIncidents())
                .totalRawAffectedCustomers(currentRecord.getRawActiveCustomerOutages())
                .uniqueIncidentsCount(currentRecord.getActiveIncidents())
                .uniqueAffectedCustomers(currentRecord.getActiveCustomerOutages())
                .build();
    }

    private Outage compareAndBuildOutage(
            ActiveOutages.OutageRecord currentRecord, ActiveOutages.OutageRecord previousRecord) {

        int uniqueIncidentsCount;
        int uniqueAffectedCustomers;
        if (previousRecord != null) {
            uniqueIncidentsCount =
                    Math.max(0, currentRecord.getActiveIncidents() - previousRecord.getActiveIncidents());
            uniqueAffectedCustomers =
                    Math.max(0, currentRecord.getActiveCustomerOutages() - previousRecord.getActiveCustomerOutages());
        } else {
            uniqueIncidentsCount = currentRecord.getActiveIncidents();
            uniqueAffectedCustomers = currentRecord.getActiveCustomerOutages();
        }

        return Outage.builder()
                .totalIncidents(currentRecord.getActiveIncidents())
                .totalAffectedCustomers(currentRecord.getActiveCustomerOutages())
                .totalRawIncidents(currentRecord.getRawActiveIncidents())
                .totalRawAffectedCustomers(currentRecord.getRawActiveCustomerOutages())
                .uniqueIncidentsCount(uniqueIncidentsCount)
                .uniqueAffectedCustomers(uniqueAffectedCustomers)
                .outageIncidentMap(new HashMap<>())
                .build();
    }

    private ActiveOutages.OutageRecord findPreviousRecordByRegion(ActiveOutages previousOutage, String region) {
        return previousOutage.getOutageRecords().stream()
                .filter(r -> r.getRegion().equals(region))
                .findFirst()
                .orElse(null);
    }

    private void calculateSystemTotals(Map<String, Outage> outagesByRegion) {
        Outage systemOutage = Outage.builder()
                .totalAffectedCustomers(0)
                .totalIncidents(0)
                .totalRawAffectedCustomers(0)
                .totalRawIncidents(0)
                .uniqueAffectedCustomers(0)
                .uniqueIncidentsCount(0)
                .outageIncidentMap(new HashMap<>())
                .build();

        for (Map.Entry<String, Outage> entry : outagesByRegion.entrySet()) {
            Outage regionalOutage = entry.getValue();
            systemOutage.setTotalIncidents(systemOutage.getTotalIncidents() + regionalOutage.getTotalIncidents());
            systemOutage.setTotalAffectedCustomers(
                    systemOutage.getTotalAffectedCustomers() + regionalOutage.getTotalAffectedCustomers());
            if (regionalOutage.getTotalRawIncidents() != null) {
                systemOutage.setTotalRawIncidents(
                        systemOutage.getTotalRawIncidents() + regionalOutage.getTotalRawIncidents());
            }
            if (regionalOutage.getTotalRawAffectedCustomers() != null) {
                systemOutage.setTotalRawAffectedCustomers(
                        systemOutage.getTotalRawAffectedCustomers() + regionalOutage.getTotalRawAffectedCustomers());
            }
            systemOutage.setUniqueIncidentsCount(
                    systemOutage.getUniqueIncidentsCount() + regionalOutage.getUniqueIncidentsCount());
            systemOutage.setUniqueAffectedCustomers(
                    systemOutage.getUniqueAffectedCustomers() + regionalOutage.getUniqueAffectedCustomers());
        }

        outagesByRegion.put(MetricConstants.SYSTEM_REGION, systemOutage);
    }
}
