package com.esource.stormmetricservice.aggregatemetrics.controller;

import java.time.ZonedDateTime;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric;
import com.esource.stormmetricservice.aggregatemetrics.repository.AggregateStormMetricRepository;
import com.esource.stormmetricservice.aggregatemetrics.service.AggregateMetricService;

import lombok.RequiredArgsConstructor;

/**
 * Retrieves aggregate metrics and accumulates them within a specified inner time frame.
 *
 * @param start Start of the window
 * @param end End of the window
 * @param regionsToFilter Regions that need to be filtered
 * @param outageGranularity The outage granularity
 * @param accumulationStart The start of the accumulation period (inclusive), defaults to start
 * @param accumulationEnd The end of the accumulation period (inclusive), defaults to end
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/overview")
public class AggregateMetricController {
    private final AggregateMetricService aggregateMetricService;
    private final AggregateStormMetricRepository aggregateStormMetricRepository;
    private static final String DEFAULT_OUTAGE_GRANULARITY_LOWEST = "5";

    @GetMapping("/aggregate")
    public ResponseEntity<AggregateMetric> getAggregateMetrics(
            @RequestParam("start") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) ZonedDateTime start,
            @RequestParam("end") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) ZonedDateTime end,
            @RequestParam(value = "regionsToFilter", required = false) List<String> regionsToFilter,
            @RequestParam(
                            value = "outageGranularity",
                            required = false,
                            defaultValue = DEFAULT_OUTAGE_GRANULARITY_LOWEST)
                    Integer outageGranularity,
            @RequestParam(value = "accumulationStart", required = false)
                    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                    ZonedDateTime accumulationStart,
            @RequestParam(value = "accumulationEnd", required = false)
                    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                    ZonedDateTime accumulationEnd) {
        AggregateMetric aggregateMetric =
                aggregateMetricService.getAggregateMetrics(start, end, accumulationStart, accumulationEnd);
        AggregateMetric filteredMetrics =
                aggregateMetricService.filterAggregateMetrics(aggregateMetric, regionsToFilter);
        AggregateMetric granulatedMetrics =
                aggregateMetricService.aggregateMetricsByGranularity(filteredMetrics, outageGranularity);
        return ResponseEntity.ok(granulatedMetrics);
    }

    @GetMapping("/stormAggregate")
    public ResponseEntity<AggregateMetric> getStormAggregateMetric(
            @RequestParam("stormId") String stormId,
            @RequestParam(value = "regionsToFilter", required = false) List<String> regionsToFilter,
            @RequestParam(
                            value = "outageGranularity",
                            required = false,
                            defaultValue = DEFAULT_OUTAGE_GRANULARITY_LOWEST)
                    Integer outageGranularity) {
        AggregateMetric aggregateMetric = aggregateStormMetricRepository
                .findByStormOverviewId(stormId)
                .orElseThrow(() -> new IllegalArgumentException("No aggregate metric found for stormId: " + stormId))
                .getAggregateMetric();
        AggregateMetric filteredMetrics =
                aggregateMetricService.filterAggregateMetrics(aggregateMetric, regionsToFilter);
        AggregateMetric granulatedMetrics =
                aggregateMetricService.aggregateMetricsByGranularity(filteredMetrics, outageGranularity);
        return ResponseEntity.ok(granulatedMetrics);
    }
}
