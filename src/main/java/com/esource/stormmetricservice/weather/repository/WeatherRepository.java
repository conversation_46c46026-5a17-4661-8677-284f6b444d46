package com.esource.stormmetricservice.weather.repository;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.weather.model.RawWeatherData;

@Repository
@RepositoryRestResource(collectionResourceRel = "weather", path = "weather")
public interface WeatherRepository extends MongoRepository<RawWeatherData, String> {
    Optional<RawWeatherData> findByTimestamp(ZonedDateTime timestamp);

    List<RawWeatherData> findByTimestampBetweenOrderByTimestampAsc(ZonedDateTime start, ZonedDateTime end);

    Optional<RawWeatherData> findFirstByOrderByTimestampDesc();
}
