package com.esource.stormmetricservice.weather.model;

import java.time.ZonedDateTime;
import java.util.Map;
import java.util.UUID;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.TimeSeries;
import org.springframework.data.mongodb.core.timeseries.Granularity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TimeSeries(
        collection = "weather",
        timeField = "timestamp",
        granularity = Granularity.HOURS,
        metaField = "weatherStations")
public class RawWeatherData {
    @Id
    private String id;

    private ZonedDateTime timestamp;
    private Map<String, WeatherStation> weatherStations; // UUID, WeatherStation
    private Map<String, Object> weatherStationMetadata;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WeatherStation {
        private UUID stationId;
        private double latitude;
        private double longitude;

        private String region; // pd_area

        private String workGroup; // what are these in DLC?
        // private List<String> territories;
        private WeatherAttributes weatherAttributes;
    }

    @Data
    @Builder
    public static class WeatherAttributes {
        private double temperatureCelsius;
        private double humidityPercent;
        private int windBearingDeg;
        private double windGustMph;
        private double windSpeedMph;
        // extend weather attributes if needed
    }
}
