package com.esource.stormmetricservice.config;

import java.util.Arrays;

import org.bson.UuidRepresentation;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.mongo.MongoProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import com.esource.stormmetricservice.aggregatemetrics.model.Overview;
import com.esource.stormmetricservice.config.converters.TimeStampConverters;
import com.esource.stormmetricservice.weather.model.RawWeatherData;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.ServerAddress;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(MongoProperties.class)
@EnableMongoRepositories(
        basePackages = {
            "com.esource.stormmetricservice.metrics.repository",
            "com.esource.stormmetricservice.aggregatemetrics.repository",
            "com.esource.stormmetricservice.stormevents.events.deadletter.repository",
            "com.esource.stormmetricservice.weather.repository",
            "com.esource.stormmetricservice.ami.repository"
        })
public class MongoConfig {

    private final MongoProperties mongoProperties;

    @Bean
    public MongoClient mongoClient() {
        MongoClientSettings.Builder settingsBuilder = MongoClientSettings.builder()
                .uuidRepresentation(UuidRepresentation.STANDARD)
                .applyToClusterSettings(builder -> builder.hosts(Arrays.asList(new ServerAddress(
                        mongoProperties.getHost(),
                        mongoProperties.getPort() != null ? mongoProperties.getPort() : 27017))));

        if (mongoProperties.getUsername() != null && mongoProperties.getPassword() != null) {
            MongoCredential credential = MongoCredential.createCredential(
                    mongoProperties.getUsername(),
                    mongoProperties.getAuthenticationDatabase() != null
                            ? mongoProperties.getAuthenticationDatabase()
                            : "admin",
                    mongoProperties.getPassword());
            settingsBuilder.credential(credential);
        }

        return MongoClients.create(settingsBuilder.build());
    }

    @Bean
    public MongoCustomConversions customConversions() {
        return new MongoCustomConversions(Arrays.asList(
                new TimeStampConverters.DateToZonedDateTimeConverter(),
                new TimeStampConverters.ZonedDateTimeToDateConverter()));
    }

    @Primary
    @Bean
    public MongoDatabaseFactory mongoDatabaseFactory(MongoClient mongoClient) {
        return new SimpleMongoClientDatabaseFactory(
                mongoClient, mongoProperties.getDatabase() != null ? mongoProperties.getDatabase() : "storm_metrics");
    }

    @Primary
    @Bean
    public MappingMongoConverter mappingMongoConverter(
            MongoDatabaseFactory databaseFactory, MongoCustomConversions customConversions) {
        DefaultDbRefResolver dbRefResolver = new DefaultDbRefResolver(databaseFactory);
        MongoMappingContext mappingContext = new MongoMappingContext();
        mappingContext.setSimpleTypeHolder(customConversions.getSimpleTypeHolder());

        MappingMongoConverter converter = new MappingMongoConverter(dbRefResolver, mappingContext);
        converter.setCustomConversions(customConversions);
        converter.afterPropertiesSet();

        return converter;
    }

    @Primary
    @Bean
    public MongoTemplate mongoTemplate(MongoDatabaseFactory databaseFactory, MappingMongoConverter converter) {
        return new MongoTemplate(databaseFactory, converter);
    }

    @Bean
    public CommandLineRunner createCollections(MongoTemplate mongoTemplate) {
        return args -> {
            try {
                mongoTemplate.createCollection(Overview.class);
            } catch (Exception e) {
                log.info("Overview Collection already exists");
            }
            try {
                mongoTemplate.createCollection(RawWeatherData.class);
            } catch (Exception e) {
                log.info("RawWeather Collection already exists");
            }
        };
    }
}
