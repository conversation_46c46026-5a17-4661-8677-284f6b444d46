package com.esource.stormmetricservice.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

@Configuration
public class RabbitMQConfig {

    @Value("${rabbitmq.exchange.stormEvent:storm.storm-event-exchange}")
    private String stormEventExchange;

    @Value("${rabbitmq.queue.stormEnd:storm.storm-event.metric-service.storm-end.queue}")
    private String stormEndQueue;

    @Value("${rabbitmq.queue.aggregateOverview:storm.storm-event.metric-service.aggregate-overview.queue}")
    private String aggregateOverviewQueue;

    @Value("${rabbitmq.routingKey.routingKey:storm.storm-event.metric-service.#}")
    private String stormEventRoutingKey;

    @Value("${rabbitmq.routingKey.aggregateRoutingKey:storm.storm-aggregate-event.metric-service.#}")
    private String stormAggregateEventRoutingKey;

    @Bean
    TopicExchange exchange() {
        return new TopicExchange(stormEventExchange);
    }

    @Bean(name = "stormEndQueue")
    Queue stormEndQueue() {
        return new Queue(stormEndQueue, true);
    }

    @Bean(name = "aggregateOverviewQueue")
    Queue aggregateOverviewQueue() {

        return new Queue(aggregateOverviewQueue, true);
    }

    @Bean
    public Binding bindingStormEvent(@Qualifier(value = "stormEndQueue") Queue stormEndQueue, TopicExchange exchange) {
        return BindingBuilder.bind(stormEndQueue).to(exchange).with(stormEventRoutingKey);
    }

    @Bean
    public Binding bindingAggregateOverview(
            @Qualifier(value = "aggregateOverviewQueue") Queue aggregateOverviewQueue, TopicExchange exchange) {
        return BindingBuilder.bind(aggregateOverviewQueue).to(exchange).with(stormAggregateEventRoutingKey);
    }

    @Bean
    public Jackson2JsonMessageConverter converter() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        return new Jackson2JsonMessageConverter(objectMapper);
    }
}
