package com.esource.stormmetricservice.config.weather;

import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;

/**
 * Configuration properties for weather filtering behavior.
 * Supports both default region-based filtering and client-specific workgroup-based filtering.
 */
@ConfigurationProperties(prefix = "weather.filtering")
@Validated
public record WeatherFilteringProperties(
        @NotBlank(message = "Default parent zone must be provided") String defaultParentZone,
        Map<String, WorkgroupConfig> workgroupClients) {

    /**
     * Configuration for clients that use workgroup-based weather filtering.
     */
    @Validated
    public record WorkgroupConfig(
            @NotBlank(message = "Workgroup name must be provided") String workgroupName) {}
}
