package com.esource.stormmetricservice.config.ami;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;

@ConfigurationProperties(prefix = "ami.ingestion.retry")
@Validated
public record AmiRetryProperties(
        @PositiveOrZero(message = "minimumSuccessRate must be greater or equal to 0") double minimumSuccessRate,
        @Positive(message = "retryDelayHours must be greater than 0") int retryDelayHours) {}
