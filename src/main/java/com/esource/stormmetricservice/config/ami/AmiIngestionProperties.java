package com.esource.stormmetricservice.config.ami;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;

@ConfigurationProperties(prefix = "ami.ingestion")
@Validated
public record AmiIngestionProperties(boolean enabled, DatabricksConfig databricks, AzureAuth azure) {

    @Validated
    public record DatabricksConfig(
            @NotBlank(message = "Databricks host must be provided") String host,
            @Positive(message = "Port must be greater than 0") int port,
            @NotBlank(message = "HTTP path must be provided") String httpPath,
            @NotBlank(message = "Catalog name must be provided") String catalogName) {}

    @Validated
    public record AzureAuth(
            @NotBlank(message = "Azure tenant ID must be provided") String tenantId,
            @NotBlank(message = "Azure client ID must be provided") String clientId,
            @NotBlank(message = "Azure client secret must be provided") String clientSecret,
            @NotBlank(message = "Azure scope must be provided") String scope) {}
}
