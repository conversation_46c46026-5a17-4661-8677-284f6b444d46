package com.esource.stormmetricservice.config.ami;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

@Configuration
@ConditionalOnProperty(value = "ami.ingestion.enabled", havingValue = "true")
@EnableConfigurationProperties({AmiRetryProperties.class, AmiIngestionProperties.class})
@EnableScheduling
public class AmiIngestionConfiguration {

    @Bean
    @ConditionalOnProperty(value = "ami.ingestion.enabled", havingValue = "true")
    RestTemplate databricksRestTemplate() {
        return new RestTemplate();
    }
}
