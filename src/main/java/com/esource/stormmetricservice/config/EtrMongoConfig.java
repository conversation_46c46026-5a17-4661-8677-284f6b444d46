package com.esource.stormmetricservice.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import com.mongodb.client.MongoClient;

import lombok.extern.slf4j.Slf4j;

/**
 * Configures the connection to a secondary MongoDB database named 'etr' which resides in the same MongoDB instance as the primary database
 * configured in the application. This configuration is specifically for managing etr-related data.
 * <p>
 * To properly configure this class, the following property must be set in the application's configuration file: - 'mongodb.asset.database': The name
 * of the 'etr' database in the MongoDB instance.
 * <p>
 * This class sets up a dedicated MongoTemplate ('etrDatabaseMongoTemplate') which is used by all repositories under the package
 * 'com.esource.stormmetricservice.etr.repository' to interact with the 'etr' database.
 */
@Slf4j
@Configuration
@EnableMongoRepositories(
        basePackages = "com.esource.stormmetricservice.etr.repository",
        mongoTemplateRef = EtrMongoConfig.ETR_DATABASE_MONGO_TEMPLATE)
public class EtrMongoConfig {

    public static final String ETR_DATABASE_MONGO_TEMPLATE = "etrDatabaseMongoTemplate";

    @Bean(name = "etrMongoDatabaseFactory")
    public MongoDatabaseFactory etrMongoDatabaseFactory(
            MongoClient mongoClient, @Value("${mongodb.etr.database:etr}") String etrDatabaseName) {
        if (etrDatabaseName == null || etrDatabaseName.isEmpty()) {
            throw new IllegalStateException(
                    "The 'mongodb.etr.database' property must be set in the application configuration.");
        }
        log.info("Connecting to ETR database: {}", etrDatabaseName);
        return new SimpleMongoClientDatabaseFactory(mongoClient, etrDatabaseName);
    }

    @Bean(name = ETR_DATABASE_MONGO_TEMPLATE)
    public MongoTemplate etrDatabaseMongoTemplate(
            @Qualifier("etrMongoDatabaseFactory") MongoDatabaseFactory etrMongoDatabaseFactory,
            MappingMongoConverter mappingMongoConverter) {
        return new MongoTemplate(etrMongoDatabaseFactory, mappingMongoConverter);
    }
}
