package com.esource.stormmetricservice.common.util;

public class MetricConstants {
    private MetricConstants() {}

    public static final String METRIC_TYPE_OUTAGES = "outages";

    public static final String METRIC_TYPE_CUMULATIVE_OUTAGES = "cumulative-outages";
    public static final String METRIC_TYPE_RESTORATIONS = "restorations";
    public static final String METRIC_TYPE_MEDIUM_PREDICTED_RESTORATIONS = "medium-predicted-restorations";

    public static final String METRIC_TYPE_RESOURCES = "resources";
    public static final String SYSTEM_REGION = "System";
    public static final String OUTAGE_SCALE_MEDIUM = "medium";
    public static final String ETR_RESULT = "result";
    public static final int HOURS_TO_ADD_OR_SUBTRACT = 6;
}
