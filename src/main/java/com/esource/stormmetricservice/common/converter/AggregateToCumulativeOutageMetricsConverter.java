package com.esource.stormmetricservice.common.converter;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric;
import com.esource.stormmetricservice.metrics.model.metrics.CumulativeOutageMetric;

import lombok.NonNull;

import static com.esource.stormmetricservice.metrics.model.metrics.CumulativeOutageMetric.CumulativeOutageRecord;

@Component
public class AggregateToCumulativeOutageMetricsConverter
        implements Converter<AggregateMetric, List<CumulativeOutageMetric>> {

    @Override
    public List<CumulativeOutageMetric> convert(@NonNull AggregateMetric aggregateMetric) {
        Map<String, List<AggregateMetric.OverviewMetrics>> regionalMetrics = aggregateMetric.getRegionalMetrics();

        if (regionalMetrics.isEmpty()) {
            return Collections.emptyList();
        }

        String regionalMetricsFirstKey = regionalMetrics.keySet().iterator().next();
        List<AggregateMetric.OverviewMetrics> referenceMetrics = regionalMetrics.get(regionalMetricsFirstKey);

        return IntStream.range(0, referenceMetrics.size())
                .mapToObj(metricPosition -> createCumulativeMetric(
                        metricPosition, referenceMetrics.get(metricPosition).getTimestamp(), regionalMetrics))
                .toList();
    }

    private CumulativeOutageMetric createCumulativeMetric(
            int metricPosition,
            @NonNull ZonedDateTime timestamp,
            @NonNull Map<String, List<AggregateMetric.OverviewMetrics>> regionalMetrics) {
        List<CumulativeOutageRecord> records = regionalMetrics.keySet().stream()
                .map(region -> createCumulativeRecord(
                        region, regionalMetrics.get(region).get(metricPosition)))
                .toList();

        return CumulativeOutageMetric.builder()
                .timestamp(timestamp)
                .records(records)
                .build();
    }

    private CumulativeOutageRecord createCumulativeRecord(
            @NonNull String region, @NonNull AggregateMetric.OverviewMetrics overviewMetrics) {
        return CumulativeOutageRecord.builder()
                .region(region)
                .cumulativeCustomerOutages(overviewMetrics.getCumulativeAffectedCustomers())
                .cumulativeIncidents(overviewMetrics.getCumulativeIncidents())
                .cumulativeRestoredCustomerOutages(overviewMetrics.getCumulativeCustomersRestored())
                .cumulativeRestoredIncidents(overviewMetrics.getCumulativeRestorations())
                .build();
    }
}
