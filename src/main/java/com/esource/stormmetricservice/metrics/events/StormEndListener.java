package com.esource.stormmetricservice.metrics.events;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateStormMetric;
import com.esource.stormmetricservice.ami.service.AmiChangeIngestionQueueService;
import com.esource.stormmetricservice.metrics.service.AbstractMetricCollector;
import com.esource.stormmetricservice.metrics.service.similarity.StormSimilarityService;
import com.esource.stormmetricservice.metrics.service.summarizer.StormSummarizerInterface;
import com.esource.stormmetricservice.stormevents.model.messaging.StormEndEventProcessed;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class StormEndListener {
    private final List<AbstractMetricCollector> collectors;
    private final StormSummarizerInterface stormSummarizerService;
    private final StormSimilarityService stormSimilarityService;
    private final Optional<AmiChangeIngestionQueueService> amiChangeIngestionQueueService;

    @Value("${storm.similarity.check.interval:30}")
    private long similarityCheckIntervalSeconds;

    private final AtomicLong lastSimilarityCalculationTime = new AtomicLong(0);

    @EventListener
    public void onStormEndEventProcessed(StormEndEventProcessed event) {
        processMetrics(event);
        queueDatabricksIngestion(event);
        checkAndCalculateSimilarities();
    }

    private void processMetrics(StormEndEventProcessed event) {
        collectors.forEach(collector -> collector.processEvent(event.getEndStormEvent()));

        AggregateStormMetric aggregateStormMetric = stormSummarizerService.preAggregate(event.getEndStormEvent());
        stormSummarizerService.summarize(event.getEndStormEvent(), aggregateStormMetric);
    }

    private void queueDatabricksIngestion(StormEndEventProcessed event) {
        amiChangeIngestionQueueService.ifPresent(service -> {
            log.info("Queueing Databricks ingestion for storm event");
            service.queueStormEvent(event.getEndStormEvent());
        });
    }

    private void checkAndCalculateSimilarities() {
        long currentTime = System.currentTimeMillis();
        long lastCalculation = lastSimilarityCalculationTime.get();
        long elapsedTime = currentTime - lastCalculation;

        if (elapsedTime >= TimeUnit.SECONDS.toMillis(similarityCheckIntervalSeconds)) {
            log.info("Calculating storm similarities after {} seconds", TimeUnit.MILLISECONDS.toSeconds(elapsedTime));
            stormSimilarityService.calculateStormSimilarities();
            lastSimilarityCalculationTime.set(currentTime);
        } else {
            log.debug(
                    "Skipping similarity calculation. Only {} seconds elapsed since last calculation",
                    TimeUnit.MILLISECONDS.toSeconds(elapsedTime));
        }
    }
}
