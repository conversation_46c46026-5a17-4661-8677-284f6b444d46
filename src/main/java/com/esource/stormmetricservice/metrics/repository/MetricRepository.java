package com.esource.stormmetricservice.metrics.repository;

import java.util.Optional;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.metrics.model.StormMetric;

@Repository
public interface MetricRepository extends MongoRepository<StormMetric, String> {

    Optional<StormMetric> findByStormId(ObjectId stormId);

    Optional<StormMetric> findByStormIdAndMetricType(ObjectId stormId, String metricType);
}
