package com.esource.stormmetricservice.metrics.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.metrics.model.StormRetrospective;

@Repository
@RepositoryRestResource(collectionResourceRel = "retrospectives", path = "retrospectives")
public interface StormRetrospectiveRepository extends MongoRepository<StormRetrospective, String> {

    public List<StormRetrospective> findByStormIdIn(List<String> stormIDs);

    @NonNull public Page<StormRetrospective> findAll(@NonNull Pageable pageable);

    public List<StormRetrospective> findAllByOrderByIdDesc();

    public Optional<StormRetrospective> findByStormId(String stormId);
}
