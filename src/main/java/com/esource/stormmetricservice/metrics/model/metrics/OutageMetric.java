package com.esource.stormmetricservice.metrics.model.metrics;

import java.util.List;

import com.esource.stormmetricservice.metrics.model.Metric;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
public class OutageMetric extends Metric {
    List<OutageRecord> records;

    @Data
    @Builder
    public static class OutageRecord {
        String region;

        Integer activeCustomerOutages;

        Integer activeIncidents;
    }
}
