package com.esource.stormmetricservice.metrics.model.metrics;

import java.util.List;

import com.esource.stormmetricservice.metrics.model.Metric;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
public class PredictionMetric extends Metric {
    List<PredictionRecord> records;

    String sessionId;

    @Data
    @Builder
    public static class PredictionRecord {
        String region;

        Double hoursToRestoration;

        String type;
    }
}
