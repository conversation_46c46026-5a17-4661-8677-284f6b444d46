package com.esource.stormmetricservice.metrics.model;

import java.time.ZonedDateTime;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document(collection = "metric_document")
public class StormMetric {

    List<Metric> metrics;

    ObjectId stormId;

    String metricType;

    StormOverview stormData;

    ZonedDateTime lastUpdated;
}
