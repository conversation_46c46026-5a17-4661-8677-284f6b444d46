package com.esource.stormmetricservice.metrics.model;

import java.util.List;
import java.util.Map;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
@Document(collection = "storm_retrospective")
public class StormRetrospective {

    public static final String WIND_GUST_OVER_20 = "over20mph";
    public static final String WIND_GUST_OVER_30 = "over30mph";
    public static final String WIND_GUST_OVER_40 = "over40mph";
    public static final double DEFAULT_THRESHOLD = 0.7;

    @Id
    private String id;

    private String stormId;

    private StormOverview stormOverview;

    private Map<String, StormEndSummary> stormEndSummary;

    private List<SimilarStorm> similarStorms; // {stormId: "123", similarity: 0.8}

    @Builder
    @Data
    public static class StormEndSummary {
        private Double totalCumulativeIncidents;
        private Double totalCumulativeCustomersAffected;
        private Map<String, Integer> windGustThresholds;
        private Double averageWindSpeed;

        // WindGustThresholds: {over20: 400, over30: 200, over40: 100}
        // countsByHour ... for any weather station in a region
    }

    @Builder
    @Data
    public static class SimilarStorm {
        private String stormId;
        private Double matchedThreshold;

        @Deprecated
        private Double similarityRanking;
    }
}
