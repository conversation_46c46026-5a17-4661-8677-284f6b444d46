package com.esource.stormmetricservice.metrics.model.metrics;

import java.util.List;

import com.esource.stormmetricservice.metrics.model.Metric;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
public class RestorationMetric extends Metric {
    List<RestorationRecord> records;

    @Data
    @Builder
    public static class RestorationRecord {
        String region;

        Integer restoredCustomerOutages;

        Integer restoredIncidents;
    }
}
