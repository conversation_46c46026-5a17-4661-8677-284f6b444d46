package com.esource.stormmetricservice.metrics.model.metrics;

import java.util.List;

import com.esource.stormmetricservice.metrics.model.Metric;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
public class CumulativeOutageMetric extends Metric {
    List<CumulativeOutageRecord> records;

    @Data
    @Builder
    public static class CumulativeOutageRecord {
        String region;

        Integer cumulativeCustomerOutages;

        Integer cumulativeIncidents;

        Integer cumulativeRestoredCustomerOutages;

        Integer cumulativeRestoredIncidents;
    }
}
