package com.esource.stormmetricservice.metrics.model;

import java.io.Serializable;
import java.time.ZonedDateTime;

import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StormOverview implements Serializable {

    @Indexed(unique = true)
    String id;

    @Indexed(direction = IndexDirection.DESCENDING)
    ZonedDateTime startDate;

    ZonedDateTime endDate;

    String name;
}
