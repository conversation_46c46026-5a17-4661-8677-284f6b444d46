package com.esource.stormmetricservice.metrics.model.metrics;

import java.util.List;
import java.util.Map;

import com.esource.stormmetricservice.metrics.model.Metric;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
public class ResourceMetric extends Metric {
    List<ResourceRecord> records;

    String sessionId;

    @Data
    @Builder
    public static class ResourceRecord {
        String region;

        Double totalWorkers;

        Map<String, Integer> resources;
    }
}
