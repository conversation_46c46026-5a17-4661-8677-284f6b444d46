package com.esource.stormmetricservice.metrics.service;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

import org.bson.types.ObjectId;

import com.esource.stormmetricservice.metrics.model.Metric;
import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractMetricCollector {

    protected final MetricRepository metricRepository;

    public void processEvent(EndStormEvent endStormEvent) {
        log.info(
                "Received end storm event for stormId: {}",
                endStormEvent.getStormData().getId());

        if (shouldCollect(endStormEvent)) {
            log.info("Collecting {} metric", getMetricType());
            StormMetric stormMetric = buildStormMetric(endStormEvent, collectMetric(endStormEvent));
            metricRepository.save(stormMetric);
        } else {
            log.info(
                    "{} metric already present in DB for stormId: {}",
                    getMetricType(),
                    endStormEvent.getStormData().getId());
        }
    }

    protected boolean shouldCollect(EndStormEvent endStormEvent) {
        return !metricRepository
                .findByStormIdAndMetricType(
                        new ObjectId(endStormEvent.getStormData().getId()), getMetricType())
                .isPresent();
    }

    protected StormMetric buildStormMetric(EndStormEvent endStormEvent, List<Metric> metrics) {
        return StormMetric.builder()
                .stormId(new ObjectId(endStormEvent.getStormData().getId()))
                .metricType(getMetricType())
                .stormData(endStormEvent.getStormData())
                .metrics(metrics)
                .lastUpdated(ZonedDateTime.now().truncatedTo(ChronoUnit.MINUTES)) // this will make querying easier
                .build();
    }

    protected abstract String getMetricType();

    protected abstract List<Metric> collectMetric(EndStormEvent endStormEvent);
}
