package com.esource.stormmetricservice.metrics.service.similarity;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.metrics.model.StormRetrospective;
import com.esource.stormmetricservice.metrics.model.StormRetrospective.StormEndSummary;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DefaultStormSimilarityEvaluator implements StormSimilarityEvaluator {

    private static final String SYSTEM_REGION = "System";
    private static final int STORM_DATE_PROXIMITY_THRESHOLD = 60; // 60 days

    @Override
    public boolean isSimilar(StormRetrospective storm1, StormRetrospective storm2, double threshold) {
        boolean isDurationMatch = matchesDuration(storm1, storm2, threshold);
        boolean isGustMatch = matchesWindGusts(storm1, storm2, threshold);
        boolean isWindSpeedMatch = matchesAverageWindSpeed(storm1, storm2, threshold);
        boolean isStartDateMatch = matchesEventStartDate(storm1, storm2);

        log.info("Evaluating similarity between storms '{}' and '{}':", storm1.getStormId(), storm2.getStormId());
        log.info(
                "Duration Match: {}, Wind Gusts Match: {}, Wind Speed Match: {}, Start Date Match: {}",
                isDurationMatch,
                isGustMatch,
                isWindSpeedMatch,
                isStartDateMatch);

        return isDurationMatch && isGustMatch && isWindSpeedMatch && isStartDateMatch;
    }

    private boolean matchesDuration(StormRetrospective storm1, StormRetrospective storm2, double threshold) {
        long duration1 = getDurationInSeconds(
                storm1.getStormOverview().getStartDate(),
                storm1.getStormOverview().getEndDate());
        long duration2 = getDurationInSeconds(
                storm2.getStormOverview().getStartDate(),
                storm2.getStormOverview().getEndDate());
        return isWithinThreshold(duration1, duration2, threshold);
    }

    private boolean matchesWindGusts(StormRetrospective storm1, StormRetrospective storm2, double threshold) {
        Map<String, Integer> gusts1 = getSafeWindGusts(storm1);
        Map<String, Integer> gusts2 = getSafeWindGusts(storm2);
        return isWithinThreshold(
                        gusts1.getOrDefault(StormRetrospective.WIND_GUST_OVER_20, 0),
                        gusts2.getOrDefault(StormRetrospective.WIND_GUST_OVER_20, 0),
                        threshold)
                && isWithinThreshold(
                        gusts1.getOrDefault(StormRetrospective.WIND_GUST_OVER_30, 0),
                        gusts2.getOrDefault(StormRetrospective.WIND_GUST_OVER_30, 0),
                        threshold)
                && isWithinThreshold(
                        gusts1.getOrDefault(StormRetrospective.WIND_GUST_OVER_40, 0),
                        gusts2.getOrDefault(StormRetrospective.WIND_GUST_OVER_40, 0),
                        threshold);
    }

    private boolean matchesAverageWindSpeed(StormRetrospective storm1, StormRetrospective storm2, double threshold) {
        double windSpeed1 = getSafeAverageWindSpeed(storm1);
        double windSpeed2 = getSafeAverageWindSpeed(storm2);
        return isWithinThreshold(windSpeed1, windSpeed2, threshold);
    }

    private boolean matchesEventStartDate(StormRetrospective storm1, StormRetrospective storm2) {
        ZonedDateTime startDate1 = storm1.getStormOverview().getStartDate();
        ZonedDateTime startDate2 = storm2.getStormOverview().getStartDate();
        long daysBetween = java.time.Duration.between(startDate1, startDate2).toDays();
        return Math.abs(daysBetween) <= STORM_DATE_PROXIMITY_THRESHOLD;
    }

    private Map<String, Integer> getSafeWindGusts(StormRetrospective storm) {
        StormEndSummary summary = storm.getStormEndSummary()
                .getOrDefault(
                        SYSTEM_REGION,
                        StormRetrospective.StormEndSummary.builder().build());
        return summary.getWindGustThresholds() != null ? summary.getWindGustThresholds() : new HashMap<>();
    }

    private double getSafeAverageWindSpeed(StormRetrospective storm) {
        StormEndSummary summary = storm.getStormEndSummary()
                .getOrDefault(
                        SYSTEM_REGION,
                        StormRetrospective.StormEndSummary.builder().build());
        return summary.getAverageWindSpeed() != null ? summary.getAverageWindSpeed() : 0.0;
    }

    private boolean isWithinThreshold(double value1, double value2, double threshold) {
        double max = Math.max(value1, value2);
        double min = Math.min(value1, value2);
        return max == 0 || (1 - (min / max)) <= (1 - threshold);
    }

    private long getDurationInSeconds(ZonedDateTime start, ZonedDateTime end) {
        return java.time.Duration.between(start, end).getSeconds();
    }
}
