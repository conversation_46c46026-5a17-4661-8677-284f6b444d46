package com.esource.stormmetricservice.metrics.service.collectors;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.etr.model.ActiveOutages;
import com.esource.stormmetricservice.etr.repository.ActiveOutagesRepository;
import com.esource.stormmetricservice.metrics.model.*;
import com.esource.stormmetricservice.metrics.model.metrics.OutageMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;
import com.esource.stormmetricservice.metrics.service.AbstractMetricCollector;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

@Component
public class OutageMetricCollector extends AbstractMetricCollector {
    private final ActiveOutagesRepository outageRepository;

    public OutageMetricCollector(MetricRepository metricRepository, ActiveOutagesRepository outageRepository) {
        super(metricRepository);
        this.outageRepository = outageRepository;
    }

    @Override
    protected String getMetricType() {
        return MetricConstants.METRIC_TYPE_OUTAGES;
    }

    @Override
    protected List<Metric> collectMetric(EndStormEvent endStormEvent) {
        ZonedDateTime startDate = endStormEvent.getStormData().getStartDate();
        ZonedDateTime endDate = endStormEvent.getStormData().getEndDate();

        List<ActiveOutages> outages = outageRepository.findByCreatedTimestampBetween(
                startDate.minusHours(MetricConstants.HOURS_TO_ADD_OR_SUBTRACT),
                endDate.plusHours(MetricConstants.HOURS_TO_ADD_OR_SUBTRACT));

        return buildOutageMetrics(outages);
    }

    private List<Metric> buildOutageMetrics(List<ActiveOutages> outages) {
        return outages.stream()
                .map(outage -> {
                    List<OutageMetric.OutageRecord> outageMetricRecords = outage.getOutageRecords().stream()
                            .map(record -> OutageMetric.OutageRecord.builder()
                                    .region(record.getRegion())
                                    .activeCustomerOutages(record.getActiveCustomerOutages())
                                    .activeIncidents(record.getActiveIncidents())
                                    .build())
                            .collect(Collectors.toList());

                    return OutageMetric.builder()
                            .timestamp(outage.getCreatedTimestamp())
                            .records(outageMetricRecords)
                            .build();
                })
                .collect(Collectors.toList());
    }
}
