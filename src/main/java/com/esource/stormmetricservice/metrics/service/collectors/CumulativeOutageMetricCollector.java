package com.esource.stormmetricservice.metrics.service.collectors;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric;
import com.esource.stormmetricservice.aggregatemetrics.service.AggregateMetricService;
import com.esource.stormmetricservice.common.converter.AggregateToCumulativeOutageMetricsConverter;
import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.metrics.model.Metric;
import com.esource.stormmetricservice.metrics.model.metrics.CumulativeOutageMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;
import com.esource.stormmetricservice.metrics.service.AbstractMetricCollector;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import lombok.NonNull;

import static com.esource.stormmetricservice.common.util.MetricConstants.HOURS_TO_ADD_OR_SUBTRACT;

@Component
public class CumulativeOutageMetricCollector extends AbstractMetricCollector {

    public static final int OUTAGE_GRANULARITY = 10;
    private final AggregateMetricService aggregateMetricService;
    private final AggregateToCumulativeOutageMetricsConverter converter;

    public CumulativeOutageMetricCollector(
            @NonNull MetricRepository metricRepository,
            @NonNull AggregateMetricService aggregateMetricService,
            @NonNull AggregateToCumulativeOutageMetricsConverter converter) {
        super(metricRepository);
        this.aggregateMetricService = aggregateMetricService;
        this.converter = converter;
    }

    @Override
    protected String getMetricType() {
        return MetricConstants.METRIC_TYPE_CUMULATIVE_OUTAGES;
    }

    @Override
    protected List<Metric> collectMetric(EndStormEvent endStormEvent) {
        ZonedDateTime startDate = endStormEvent.getStormData().getStartDate();
        ZonedDateTime endDate = endStormEvent.getStormData().getEndDate();

        AggregateMetric aggregateMetric = aggregateMetricService.getAggregateMetrics(
                startDate, endDate.plusHours(HOURS_TO_ADD_OR_SUBTRACT), null, null);
        AggregateMetric granulatedMetric =
                aggregateMetricService.aggregateMetricsByGranularity(aggregateMetric, OUTAGE_GRANULARITY);

        List<CumulativeOutageMetric> cumulativeOutageMetrics = converter.convert(granulatedMetric);
        return new ArrayList<>(cumulativeOutageMetrics);
    }
}
