package com.esource.stormmetricservice.metrics.service.collectors;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.etr.model.ETR;
import com.esource.stormmetricservice.etr.repository.ETRRepository;
import com.esource.stormmetricservice.metrics.model.Metric;
import com.esource.stormmetricservice.metrics.model.metrics.ResourceMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;
import com.esource.stormmetricservice.metrics.service.AbstractMetricCollector;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ResourceMetricCollector extends AbstractMetricCollector {

    private final ETRRepository etrRepository;

    public ResourceMetricCollector(MetricRepository metricRepository, ETRRepository etrRepository) {
        super(metricRepository);
        this.etrRepository = etrRepository;
    }

    @Override
    public String getMetricType() {
        return MetricConstants.METRIC_TYPE_RESOURCES;
    }

    @Override
    public List<Metric> collectMetric(EndStormEvent endStormEvent) {
        List<ETR> etrs = etrRepository.findByStormIdAndEtrType(
                endStormEvent.getStormData().getId(), MetricConstants.ETR_RESULT);

        return etrs.stream().map(this::buildPredictionMetrics).collect(Collectors.toList());
    }

    private Metric buildPredictionMetrics(ETR etr) {
        if (etr.getResources() == null || etr.getResources().isEmpty()) {
            return buildMetricFromSystemResources(etr);
        }

        List<ResourceMetric.ResourceRecord> resourceRecords = buildResourceRecords(etr);
        return ResourceMetric.builder()
                .sessionId(etr.getSessionId().toHexString())
                .timestamp(etr.getGenerationTime())
                .records(resourceRecords)
                .build();
    }

    private List<ResourceMetric.ResourceRecord> buildResourceRecords(ETR etr) {
        boolean containsSystemRegion = containsSystemRegion(etr.getResources());

        if (containsSystemRegion) {
            return buildResourceRecordsForSystemRegion(etr);
        } else {
            return buildResourceRecordsForAllRegions(etr);
        }
    }

    private boolean containsSystemRegion(List<ETR.Resources> resources) {
        return resources.stream()
                .anyMatch(resource -> MetricConstants.SYSTEM_REGION.equals(resource.getTerritoryName()));
    }

    private List<ResourceMetric.ResourceRecord> buildResourceRecordsForSystemRegion(ETR etr) {
        ETR.Resources systemRegionResource = etr.getResources().stream()
                .filter(resource -> MetricConstants.SYSTEM_REGION.equals(resource.getTerritoryName()))
                .findFirst()
                .orElseThrow();

        ResourceMetric.ResourceRecord record = buildRecordForResource(systemRegionResource);
        return List.of(record);
    }

    private List<ResourceMetric.ResourceRecord> buildResourceRecordsForAllRegions(ETR etr) {
        List<ResourceMetric.ResourceRecord> records =
                etr.getResources().stream().map(this::buildRecordForResource).collect(Collectors.toList());

        ResourceMetric.ResourceRecord systemRegionAggregateRecord = buildAggregateSystemRegionRecord(records);
        records.add(systemRegionAggregateRecord);
        return records;
    }

    private ResourceMetric.ResourceRecord buildRecordForResource(ETR.Resources resource) {
        double totalWorkers = resource.getResources().values().stream()
                .mapToInt(Integer::intValue)
                .asDoubleStream()
                .sum();

        return ResourceMetric.ResourceRecord.builder()
                .region(resource.getTerritoryName())
                .totalWorkers(totalWorkers)
                .resources(resource.getResources())
                .build();
    }

    private ResourceMetric.ResourceRecord buildAggregateSystemRegionRecord(
            List<ResourceMetric.ResourceRecord> records) {
        double totalWorkers = records.stream()
                .mapToDouble(ResourceMetric.ResourceRecord::getTotalWorkers)
                .sum();

        Map<String, Integer> aggregatedResources = records.stream()
                .map(ResourceMetric.ResourceRecord::getResources)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, Integer::sum));

        return ResourceMetric.ResourceRecord.builder()
                .region(MetricConstants.SYSTEM_REGION)
                .totalWorkers(totalWorkers)
                .resources(aggregatedResources)
                .build();
    }

    private Metric buildMetricFromSystemResources(ETR etr) {
        ResourceMetric.ResourceRecord record = buildSystemResourceRecord(etr.getSystemResources());
        return ResourceMetric.builder()
                .sessionId(etr.getSessionId().toHexString())
                .timestamp(etr.getGenerationTime())
                .records(List.of(record))
                .build();
    }

    private ResourceMetric.ResourceRecord buildSystemResourceRecord(Map<String, Integer> systemResources) {
        double totalWorkers = systemResources.values().stream()
                .mapToInt(Integer::intValue)
                .asDoubleStream()
                .sum();

        return ResourceMetric.ResourceRecord.builder()
                .region(MetricConstants.SYSTEM_REGION)
                .totalWorkers(totalWorkers)
                .resources(systemResources)
                .build();
    }
}
