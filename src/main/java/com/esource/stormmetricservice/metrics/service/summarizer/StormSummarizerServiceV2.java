package com.esource.stormmetricservice.metrics.service.summarizer;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric;
import com.esource.stormmetricservice.aggregatemetrics.model.AggregateMetric.OverviewMetrics;
import com.esource.stormmetricservice.aggregatemetrics.model.AggregateStormMetric;
import com.esource.stormmetricservice.aggregatemetrics.repository.AggregateStormMetricRepository;
import com.esource.stormmetricservice.aggregatemetrics.service.AggregateMetricService;
import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.model.StormRetrospective;
import com.esource.stormmetricservice.metrics.model.StormRetrospective.StormEndSummary;
import com.esource.stormmetricservice.metrics.model.metrics.CumulativeOutageMetric;
import com.esource.stormmetricservice.metrics.model.metrics.CumulativeOutageMetric.CumulativeOutageRecord;
import com.esource.stormmetricservice.metrics.repository.StormRetrospectiveRepository;
import com.esource.stormmetricservice.metrics.service.MetricService;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class StormSummarizerServiceV2 implements StormSummarizerInterface {

    private final StormRetrospectiveRepository stormRetrospectiveRepository;
    private final MetricService metricService;
    private final AggregateMetricService aggregateMetricService;
    private final AggregateStormMetricRepository aggregateStormMetricRepository;

    private static final int END_WINDOW_MINUTES = 120;

    @Override
    public AggregateStormMetric preAggregate(EndStormEvent event) {
        String stormId = event.getStormData().getId();

        return aggregateStormMetricRepository
                .findByStormOverviewId(stormId)
                .orElseGet(() -> createAndSaveAggregateStormMetric(event));
    }

    @Override
    public StormRetrospective summarize(EndStormEvent event, AggregateStormMetric aggregateStormMetric) {
        String stormId = event.getStormData().getId();

        return stormRetrospectiveRepository
                .findByStormId(stormId)
                .orElseGet(() -> createAndSaveStormRetrospective(event, aggregateStormMetric.getAggregateMetric()));
    }

    private AggregateStormMetric createAndSaveAggregateStormMetric(EndStormEvent event) {
        AggregateMetric aggregateMetric = aggregateMetricService.getAggregateMetrics(
                event.getStormData().getStartDate(), event.getStormData().getEndDate(), null, null);

        AggregateStormMetric aggregateStormMetric = AggregateStormMetric.builder()
                .stormOverview(event.getStormData())
                .aggregateMetric(aggregateMetric)
                .build();

        log.info(
                "Saving AggregateStormMetric for stormId: {}",
                event.getStormData().getId());
        return aggregateStormMetricRepository.save(aggregateStormMetric);
    }

    private StormRetrospective createAndSaveStormRetrospective(EndStormEvent event, AggregateMetric aggregateMetric) {
        String stormId = event.getStormData().getId();

        Optional<StormMetric> cumulativeOutageMetric =
                metricService.getMetricByStormIdAndType(stormId, MetricConstants.METRIC_TYPE_CUMULATIVE_OUTAGES, false);

        Map<String, StormEndSummary> stormEndSummaryMap = createStormEndSummaryMap(
                cumulativeOutageMetric.orElse(null),
                aggregateMetric,
                event.getStormData().getEndDate());

        StormRetrospective newRetrospective = StormRetrospective.builder()
                .stormId(stormId)
                .stormOverview(event.getStormData())
                .stormEndSummary(stormEndSummaryMap)
                .build();

        return stormRetrospectiveRepository.save(newRetrospective);
    }

    private Map<String, StormEndSummary> createStormEndSummaryMap(
            StormMetric cumulativeOutageMetric, AggregateMetric aggregateMetric, ZonedDateTime stormEndDate) {

        Map<String, StormEndSummary> stormEndSummaryMap = new HashMap<>();

        if (cumulativeOutageMetric == null) {
            return stormEndSummaryMap;
        }

        Optional<CumulativeOutageMetric> latestCumulativeMetric =
                getLatestCumulativeMetric(cumulativeOutageMetric, stormEndDate);

        if (latestCumulativeMetric.isEmpty()) {
            return stormEndSummaryMap;
        }

        Map<String, List<OverviewMetrics>> allMetricsPerRegion = aggregateMetric.getRegionalMetrics();

        for (CumulativeOutageRecord record : latestCumulativeMetric.get().getRecords()) {
            String region = record.getRegion();
            List<OverviewMetrics> regionMetrics = allMetricsPerRegion.getOrDefault(region, Collections.emptyList());

            StormEndSummary summary = createRegionSummary(record, regionMetrics);
            stormEndSummaryMap.put(region, summary);
        }

        return stormEndSummaryMap;
    }

    private Optional<CumulativeOutageMetric> getLatestCumulativeMetric(
            StormMetric cumulativeOutageMetric, ZonedDateTime stormEndDate) {
        return cumulativeOutageMetric.getMetrics().stream()
                .map(CumulativeOutageMetric.class::cast)
                .filter(metric -> isWithinEndWindow(metric.getTimestamp(), stormEndDate))
                .max(Comparator.comparing(CumulativeOutageMetric::getTimestamp));
    }

    private StormEndSummary createRegionSummary(CumulativeOutageRecord record, List<OverviewMetrics> regionMetrics) {
        double averageWindSpeed = regionMetrics.stream()
                .mapToDouble(OverviewMetrics::getAverageWindSpeed)
                .average()
                .orElse(0.0);

        OverviewMetrics latestMetric = regionMetrics.stream()
                .max(Comparator.comparing(OverviewMetrics::getTimestamp))
                .orElse(null);

        Map<String, Integer> windGustThresholds = latestMetric != null && latestMetric.getWindGustThresholds() != null
                ? latestMetric.getWindGustThresholds()
                : Map.of("over20mph", 0, "over30mph", 0, "over40mph", 0);

        return StormEndSummary.builder()
                .totalCumulativeIncidents((double) record.getCumulativeIncidents())
                .totalCumulativeCustomersAffected((double) record.getCumulativeCustomerOutages())
                .windGustThresholds(windGustThresholds)
                .averageWindSpeed(averageWindSpeed)
                .build();
    }

    private boolean isWithinEndWindow(ZonedDateTime timestamp, ZonedDateTime stormEnd) {
        ZonedDateTime windowStart = stormEnd.minusMinutes(END_WINDOW_MINUTES);
        return !timestamp.isBefore(windowStart) && !timestamp.isAfter(stormEnd);
    }
}
