package com.esource.stormmetricservice.metrics.service.similarity;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.etr.model.StormRecord;
import com.esource.stormmetricservice.etr.repository.StormRecordRepository;
import com.esource.stormmetricservice.metrics.model.StormRetrospective;
import com.esource.stormmetricservice.metrics.model.StormRetrospective.SimilarStorm;
import com.esource.stormmetricservice.metrics.repository.StormRetrospectiveRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class StormSimilarityService {

    private static final String STORM_MODE_ARCHIVE = "archive";
    private static final double DEFAULT_THRESHOLD = 0.7;

    private final StormRetrospectiveRepository stormRetrospectiveRepository;
    private final StormRecordRepository stormRecordRepository;
    private final StormSimilarityEvaluator similarityEvaluator;

    public void calculateStormSimilarities() {
        List<StormRetrospective> allStorms = stormRetrospectiveRepository.findAll();
        List<String> archivedStormIds = stormRecordRepository.findByStormMode(STORM_MODE_ARCHIVE).stream()
                .map(StormRecord::getId)
                .toList();

        allStorms.forEach(storm -> {
            List<SimilarStorm> similarStorms = allStorms.stream()
                    .filter(otherStorm -> !storm.getStormId().equals(otherStorm.getStormId()))
                    .filter(otherStorm -> archivedStormIds.contains(otherStorm.getStormId()))
                    .filter(otherStorm -> similarityEvaluator.isSimilar(storm, otherStorm, DEFAULT_THRESHOLD))
                    .map(otherStorm -> SimilarStorm.builder()
                            .stormId(otherStorm.getStormId())
                            .matchedThreshold(DEFAULT_THRESHOLD)
                            .build())
                    .collect(Collectors.toList());

            storm.setSimilarStorms(similarStorms);
            stormRetrospectiveRepository.save(storm);
        });
    }
}
