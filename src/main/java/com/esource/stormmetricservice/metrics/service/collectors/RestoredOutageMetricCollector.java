package com.esource.stormmetricservice.metrics.service.collectors;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.etr.model.ActiveOutages;
import com.esource.stormmetricservice.etr.repository.ActiveOutagesRepository;
import com.esource.stormmetricservice.metrics.model.Metric;
import com.esource.stormmetricservice.metrics.model.metrics.RestorationMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;
import com.esource.stormmetricservice.metrics.service.AbstractMetricCollector;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

@Component
public class RestoredOutageMetricCollector extends AbstractMetricCollector {

    private final ActiveOutagesRepository outageRepository;

    public RestoredOutageMetricCollector(MetricRepository metricRepository, ActiveOutagesRepository outageRepository) {
        super(metricRepository);
        this.outageRepository = outageRepository;
    }

    @Override
    protected String getMetricType() {
        return MetricConstants.METRIC_TYPE_RESTORATIONS;
    }

    @Override
    protected List<Metric> collectMetric(EndStormEvent endStormEvent) {
        ZonedDateTime startDate = endStormEvent.getStormData().getStartDate();
        ZonedDateTime endDate = endStormEvent.getStormData().getEndDate();

        List<ActiveOutages> outages = outageRepository.findByCreatedTimestampBetween(
                startDate.minusHours(MetricConstants.HOURS_TO_ADD_OR_SUBTRACT),
                endDate.plusHours(MetricConstants.HOURS_TO_ADD_OR_SUBTRACT));

        return buildOutageMetrics(outages);
    }

    private List<Metric> buildOutageMetrics(List<ActiveOutages> outages) {
        return outages.stream()
                .map(outage -> {
                    List<RestorationMetric.RestorationRecord> restorationMetricRecords =
                            outage.getOutageRecords().stream()
                                    .map(record -> RestorationMetric.RestorationRecord.builder()
                                            .region(record.getRegion())
                                            .restoredCustomerOutages(record.getRestoredCustomerOutages())
                                            .restoredIncidents(record.getRestoredIncidents())
                                            .build())
                                    .collect(Collectors.toList());

                    return RestorationMetric.builder()
                            .timestamp(outage.getCreatedTimestamp())
                            .records(restorationMetricRecords)
                            .build();
                })
                .collect(Collectors.toList());
    }
}
