package com.esource.stormmetricservice.metrics.service;

import java.util.List;
import java.util.Optional;

import org.bson.types.ObjectId;
import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.metrics.model.Metric;
import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.model.metrics.*;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Service
public class MetricService {

    private final MetricRepository metricRepository;

    public Optional<StormMetric> getMetricByStormIdAndType(String stormId, String metricType, Boolean isSystem) {
        return metricRepository
                .findByStormIdAndMetricType(new ObjectId(stormId), metricType)
                .map(stormMetric -> {
                    List<Metric> filteredMetrics;

                    switch (metricType) {
                        case MetricConstants.METRIC_TYPE_OUTAGES -> filteredMetrics =
                                filterMetrics(stormMetric.getMetrics(), OutageMetric.class, isSystem);
                        case MetricConstants.METRIC_TYPE_RESTORATIONS -> filteredMetrics =
                                filterMetrics(stormMetric.getMetrics(), RestorationMetric.class, isSystem);
                        case MetricConstants.METRIC_TYPE_MEDIUM_PREDICTED_RESTORATIONS -> filteredMetrics =
                                filterMetrics(stormMetric.getMetrics(), PredictionMetric.class, isSystem);
                        case MetricConstants.METRIC_TYPE_RESOURCES -> filteredMetrics =
                                filterMetrics(stormMetric.getMetrics(), ResourceMetric.class, isSystem);
                        case MetricConstants.METRIC_TYPE_CUMULATIVE_OUTAGES -> filteredMetrics =
                                filterMetrics(stormMetric.getMetrics(), CumulativeOutageMetric.class, isSystem);
                        default -> {
                            // Handle other metric types or throw an exception
                            return stormMetric;
                        }
                    }

                    return StormMetric.builder()
                            .metrics(filteredMetrics)
                            .stormId(stormMetric.getStormId())
                            .metricType(stormMetric.getMetricType())
                            .stormData(stormMetric.getStormData())
                            .lastUpdated(stormMetric.getLastUpdated())
                            .build();
                });
    }

    private <T extends Metric> List<Metric> filterMetrics(
            List<Metric> metrics, Class<T> metricClass, Boolean isSystem) {
        return metrics.stream()
                .filter(metricClass::isInstance)
                .map(metricClass::cast)
                .map(metric -> filterMetric(metric, isSystem))
                .toList();
    }

    Metric filterMetric(Metric metric, Boolean isSystem) {
        return switch (metric) {
            case OutageMetric outageMetric -> filterOutageMetric(outageMetric, isSystem);
            case RestorationMetric restorationMetric -> filterRestorationMetric(restorationMetric, isSystem);
            case PredictionMetric predictionMetric -> filterPredictionMetric(predictionMetric, isSystem);
            case ResourceMetric resourceMetric -> filterResourceMetric(resourceMetric, isSystem);
            case CumulativeOutageMetric cumulativeOutageMetric -> filterCumulativeOutageMetric(
                    cumulativeOutageMetric, isSystem);
            default -> metric;
        };
    }

    private PredictionMetric filterPredictionMetric(PredictionMetric metric, Boolean isSystem) {
        List<PredictionMetric.PredictionRecord> filteredRecords = metric.getRecords().stream()
                .filter(rec -> !isSystem || MetricConstants.SYSTEM_REGION.equals(rec.getRegion()))
                .toList();

        return PredictionMetric.builder()
                .records(filteredRecords)
                .timestamp(metric.getTimestamp())
                .sessionId(metric.getSessionId())
                .build();
    }

    private ResourceMetric filterResourceMetric(ResourceMetric metric, Boolean isSystem) {
        List<ResourceMetric.ResourceRecord> filteredRecords = metric.getRecords().stream()
                .filter(rec -> isSystem || !MetricConstants.SYSTEM_REGION.equals(rec.getRegion()))
                .toList();

        return ResourceMetric.builder()
                .records(filteredRecords)
                .timestamp(metric.getTimestamp())
                .sessionId(metric.getSessionId())
                .build();
    }

    private RestorationMetric filterRestorationMetric(RestorationMetric restorationMetric, Boolean isSystem) {
        List<RestorationMetric.RestorationRecord> filteredRecords = restorationMetric.getRecords().stream()
                .filter(rec -> !isSystem || MetricConstants.SYSTEM_REGION.equals(rec.getRegion()))
                .toList();

        return RestorationMetric.builder()
                .records(filteredRecords)
                .timestamp(restorationMetric.getTimestamp())
                .build();
    }

    private OutageMetric filterOutageMetric(OutageMetric outageMetric, Boolean isSystem) {
        List<OutageMetric.OutageRecord> filteredRecords = outageMetric.getRecords().stream()
                .filter(rec -> !isSystem || MetricConstants.SYSTEM_REGION.equals(rec.getRegion()))
                .toList();

        return OutageMetric.builder()
                .records(filteredRecords)
                .timestamp(outageMetric.getTimestamp())
                .build();
    }

    private CumulativeOutageMetric filterCumulativeOutageMetric(CumulativeOutageMetric outageMetric, Boolean isSystem) {
        List<CumulativeOutageMetric.CumulativeOutageRecord> filteredRecords = outageMetric.getRecords().stream()
                .filter(rec -> !isSystem || MetricConstants.SYSTEM_REGION.equals(rec.getRegion()))
                .toList();

        return CumulativeOutageMetric.builder()
                .records(filteredRecords)
                .timestamp(outageMetric.getTimestamp())
                .build();
    }
}
