package com.esource.stormmetricservice.metrics.service.collectors;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.common.util.MetricConstants;
import com.esource.stormmetricservice.etr.model.ETR;
import com.esource.stormmetricservice.etr.repository.ETRRepository;
import com.esource.stormmetricservice.metrics.model.Metric;
import com.esource.stormmetricservice.metrics.model.metrics.PredictionMetric;
import com.esource.stormmetricservice.metrics.repository.MetricRepository;
import com.esource.stormmetricservice.metrics.service.AbstractMetricCollector;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

@Component
public class PredictionMetricCollector extends AbstractMetricCollector {

    private final ETRRepository etrRepository;

    public PredictionMetricCollector(MetricRepository metricRepository, ETRRepository etrRepository) {
        super(metricRepository);
        this.etrRepository = etrRepository;
    }

    @Override
    protected String getMetricType() {
        return MetricConstants.METRIC_TYPE_MEDIUM_PREDICTED_RESTORATIONS;
    }

    @Override
    protected List<Metric> collectMetric(EndStormEvent endStormEvent) {
        List<ETR> etrs = etrRepository.findByStormIdAndEtrType(
                endStormEvent.getStormData().getId(), MetricConstants.ETR_RESULT);

        // Do we ever need to collect specific types of ETRs?
        Predicate<ETR.RegionalETR> typeFilter =
                regionalETR -> List.of(ETR.RegionalETR.Type.CURRENT, MetricConstants.ETR_RESULT)
                        .contains(regionalETR.getType());

        return etrs.stream()
                .map(etr -> buildPredictionMetrics(etr, endStormEvent, MetricConstants.OUTAGE_SCALE_MEDIUM, typeFilter))
                .collect(Collectors.toList());
    }

    private Metric buildPredictionMetrics(
            ETR etr, EndStormEvent endStormEvent, String outageScale, Predicate<ETR.RegionalETR> typeFilter) {
        List<PredictionMetric.PredictionRecord> predictionRecords = etr.getRegionalETRs().stream()
                .filter(regionalETR -> outageScale.equals(regionalETR.getOutageScale()))
                .filter(typeFilter)
                .map(regionalETR -> PredictionMetric.PredictionRecord.builder()
                        .region(regionalETR.getLocation())
                        .type(regionalETR.getType())
                        .hoursToRestoration(
                                calculateHoursToRestoration(regionalETR.getProjectedETR(), etr.getGenerationTime()))
                        .build())
                .collect(Collectors.toList());

        Double totalHoursToRestoration = predictionRecords.stream()
                .filter(record -> record.getHoursToRestoration() >= 0)
                .mapToDouble(PredictionMetric.PredictionRecord::getHoursToRestoration)
                .max()
                .orElse(0.0);

        totalHoursToRestoration = Math.round(totalHoursToRestoration * 100.0) / 100.0;

        predictionRecords.add(PredictionMetric.PredictionRecord.builder()
                .region(MetricConstants.SYSTEM_REGION)
                .type(MetricConstants.ETR_RESULT)
                .hoursToRestoration(totalHoursToRestoration)
                .build());

        return PredictionMetric.builder()
                .sessionId(etr.getSessionId().toHexString())
                .timestamp(etr.getGenerationTime())
                .records(predictionRecords)
                .build();
    }

    private Double calculateHoursToRestoration(ZonedDateTime projectedETR, ZonedDateTime generationTime) {
        long minutes = ChronoUnit.MINUTES.between(generationTime, projectedETR);
        return (double) minutes / 60;
    }
}
