package com.esource.stormmetricservice.metrics.service.summarizer;

import com.esource.stormmetricservice.aggregatemetrics.model.AggregateStormMetric;
import com.esource.stormmetricservice.metrics.model.StormRetrospective;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

public interface StormSummarizerInterface {
    AggregateStormMetric preAggregate(EndStormEvent endStormEvent);

    StormRetrospective summarize(EndStormEvent endStormEvent, AggregateStormMetric aggregateStormMetric);
}
