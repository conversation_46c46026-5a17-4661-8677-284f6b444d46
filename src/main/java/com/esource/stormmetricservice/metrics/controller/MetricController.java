package com.esource.stormmetricservice.metrics.controller;

import java.util.Optional;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.esource.stormmetricservice.metrics.model.StormMetric;
import com.esource.stormmetricservice.metrics.service.MetricService;

import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/metrics")
public class MetricController {

    private final MetricService stormMetricService;

    @GetMapping("/{metricType}/{stormId}")
    public ResponseEntity<StormMetric> getMetricsTypeByStormId(
            @PathVariable String stormId,
            @PathVariable String metricType,
            @RequestParam(value = "isSystem", defaultValue = "true") Boolean isSystem) {
        Optional<StormMetric> stormMetric = stormMetricService.getMetricByStormIdAndType(stormId, metricType, isSystem);
        return stormMetric.map(ResponseEntity::ok).orElseGet(() -> ResponseEntity.notFound()
                .build());
    }
}
