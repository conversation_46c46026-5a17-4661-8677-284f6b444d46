package com.esource.stormmetricservice.stormevents.model.messaging;

import org.springframework.context.ApplicationEvent;

public class AggregateOverviewEventProcessed extends ApplicationEvent {
    private final AggregateOverviewEvent aggregateOverviewEvent;

    public AggregateOverviewEventProcessed(Object source, AggregateOverviewEvent aggregateOverviewEvent) {
        super(source);
        this.aggregateOverviewEvent = aggregateOverviewEvent;
    }

    public AggregateOverviewEvent getAggregateOverviewEvent() {
        return aggregateOverviewEvent;
    }
}
