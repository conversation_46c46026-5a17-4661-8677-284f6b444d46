package com.esource.stormmetricservice.stormevents.model.messaging;

import org.springframework.context.ApplicationEvent;

public class StormEndEventProcessed extends ApplicationEvent {
    private final EndStormEvent endStormEvent;

    public StormEndEventProcessed(Object source, EndStormEvent endStormEvent) {
        super(source);
        this.endStormEvent = endStormEvent;
    }

    public EndStormEvent getEndStormEvent() {
        return endStormEvent;
    }
}
