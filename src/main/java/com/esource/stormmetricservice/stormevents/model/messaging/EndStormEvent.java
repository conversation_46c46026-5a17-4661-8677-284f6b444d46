package com.esource.stormmetricservice.stormevents.model.messaging;

import java.io.Serializable;
import java.time.ZonedDateTime;

import com.esource.stormmetricservice.metrics.model.StormOverview;

import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EndStormEvent implements Serializable {

    @Pattern(regexp = "stormEndEvent") String eventName;

    ZonedDateTime timestamp;

    StormOverview stormData;
}
