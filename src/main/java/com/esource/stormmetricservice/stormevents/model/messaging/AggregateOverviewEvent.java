package com.esource.stormmetricservice.stormevents.model.messaging;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Map;

import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AggregateOverviewEvent implements Serializable {

    private @Pattern(regexp = "aggregateOverviewEvent") String eventName;

    private ZonedDateTime timestamp;

    // pass in the active outage, unaggregated outage, or etr ID
    private Map<String, ? extends Serializable> metadata;
}
