package com.esource.stormmetricservice.stormevents.events.deadletter.model;

import java.time.Instant;
import java.util.Map;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document("dead_letter")
public class DeadLetter {

    @Id
    private String id;

    private String app;

    private String exchange;

    private String routingKey;

    private Instant processDate;

    private Map<String, Object> headers;

    private String payload;

    private String cause;

    private String[] stackTrace;
}
