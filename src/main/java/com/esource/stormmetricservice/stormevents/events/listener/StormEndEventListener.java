package com.esource.stormmetricservice.stormevents.events.listener;

import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;
import com.esource.stormmetricservice.stormevents.model.messaging.StormEndEventProcessed;

@Service
public class StormEndEventListener extends AbstractEventListener<EndStormEvent> {
    public StormEndEventListener(ApplicationEventPublisher eventPublisher) {
        super(eventPublisher);
    }

    @RabbitListener(queues = "#{stormEndQueue.name}", ackMode = "AUTO", errorHandler = "mongoDeadLetterErrorHandler")
    public void receiveEndStormEvent(EndStormEvent endStormEvent) {
        super.receiveEvent(endStormEvent);
    }

    @Override
    protected ApplicationEvent createApplicationEvent(Object source, EndStormEvent event) {
        return new StormEndEventProcessed(source, event);
    }
}
