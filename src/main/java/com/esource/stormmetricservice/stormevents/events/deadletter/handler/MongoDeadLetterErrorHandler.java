/**
 * Handles error messages from RabbitMQ listeners by saving them as "dead letters" in MongoDB.
 * This class is responsible for handling exceptions that occur during message processing in RabbitMQ listeners.
 * The dead letter data is stored in the dead_letter collection.
 *
 * <AUTHOR> RAMP Backend Engineers: <PERSON>, <PERSON><PERSON><PERSON>
 * @since 1.4
 */
package com.esource.stormmetricservice.stormevents.events.deadletter.handler;

import java.time.ZoneId;
import java.time.ZonedDateTime;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.listener.api.RabbitListenerErrorHandler;
import org.springframework.amqp.rabbit.support.ListenerExecutionFailedException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.esource.stormmetricservice.stormevents.events.deadletter.model.DeadLetter;
import com.esource.stormmetricservice.stormevents.events.deadletter.repository.DeadLetterRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;

import lombok.RequiredArgsConstructor;

import static org.apache.commons.lang3.exception.ExceptionUtils.getMessage;
import static org.apache.commons.lang3.exception.ExceptionUtils.getStackFrames;

@Component
@RequiredArgsConstructor
public class MongoDeadLetterErrorHandler implements RabbitListenerErrorHandler {

    @Value("${spring.application.name:Not Registered}")
    private String appName;

    private final DeadLetterRepository deadLetterRepository;

    private final ObjectMapper objectMapper;

    /**
     * Handles the error by saving the message as a dead letter in MongoDB and throwing an exception
     * to reject and not requeue the message in RabbitMQ.
     *
     * @param rawMessage The raw AMQP message that caused the error.
     * @param convertedMessage The converted Spring Messaging message, if available.
     * @param exception The exception that occurred during message processing.
     * @return This method does not return any value.
     * @throws Exception This method throws an AmqpRejectAndDontRequeueException.
     */
    @Override
    public Object handleError(
            Message rawMessage,
            Channel channel,
            org.springframework.messaging.Message<?> convertedMessage,
            ListenerExecutionFailedException exception)
            throws Exception {
        MessageProperties messageProperties = rawMessage.getMessageProperties();
        Throwable rootCause = ExceptionUtils.getRootCause(exception);
        String serializedPayload;
        try {
            Object payload =
                    convertedMessage == null ? new String(rawMessage.getBody()) : convertedMessage.getPayload();
            serializedPayload = objectMapper.writeValueAsString(payload);
        } catch (Exception e) {
            serializedPayload = "Error serializing payload: " + e.getMessage();
        }
        DeadLetter deadLetter = DeadLetter.builder()
                .app(appName)
                .exchange(messageProperties.getReceivedExchange())
                .routingKey(messageProperties.getReceivedRoutingKey())
                .headers(messageProperties.getHeaders())
                .processDate(ZonedDateTime.now(ZoneId.of("UTC")).toInstant())
                .payload(serializedPayload)
                .cause(getMessage(rootCause))
                .stackTrace(getStackFrames(rootCause))
                .build();

        deadLetterRepository.save(deadLetter);

        throw new AmqpRejectAndDontRequeueException(exception);
    }
}
