package com.esource.stormmetricservice.stormevents.events.listener;

import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractEventListener<T> {

    private final ApplicationEventPublisher eventPublisher;

    public void receiveEvent(T event) {
        log.info("Received event: {}", event);
        ApplicationEvent applicationEvent = createApplicationEvent(this, event);
        eventPublisher.publishEvent(applicationEvent);
    }

    protected abstract ApplicationEvent createApplicationEvent(Object source, T event);
}
