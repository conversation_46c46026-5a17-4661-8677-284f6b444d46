package com.esource.stormmetricservice.stormevents.events.listener;

import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEvent;
import com.esource.stormmetricservice.stormevents.model.messaging.AggregateOverviewEventProcessed;

@Service
public class AggregateOverviewEventListener extends AbstractEventListener<AggregateOverviewEvent> {
    public AggregateOverviewEventListener(ApplicationEventPublisher eventPublisher) {
        super(eventPublisher);
    }

    @RabbitListener(
            queues = "#{aggregateOverviewQueue.name}",
            ackMode = "AUTO",
            errorHandler = "mongoDeadLetterErrorHandler")
    public void receiveAggregateOverviewEvent(AggregateOverviewEvent aggregateOverviewEvent) {
        super.receiveEvent(aggregateOverviewEvent);
    }

    @Override
    protected ApplicationEvent createApplicationEvent(Object source, AggregateOverviewEvent event) {
        return new AggregateOverviewEventProcessed(source, event);
    }
}
