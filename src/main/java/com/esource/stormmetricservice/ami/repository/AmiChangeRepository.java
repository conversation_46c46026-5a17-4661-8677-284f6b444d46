package com.esource.stormmetricservice.ami.repository;

import java.time.ZonedDateTime;
import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.ami.model.AmiMeterChangeDocument;

@Repository
public interface AmiChangeRepository extends MongoRepository<AmiMeterChangeDocument, String> {

    @Query("{'timestamp': {$gte: ?0, $lte: ?1}}")
    List<AmiMeterChangeDocument> findMeterChangesByTimeRange(ZonedDateTime start, ZonedDateTime end);

    @Query(value = "{'timestamp': {$gte: ?0, $lte: ?1}}", delete = true)
    void deleteByTimeRange(ZonedDateTime start, ZonedDateTime end);
}
