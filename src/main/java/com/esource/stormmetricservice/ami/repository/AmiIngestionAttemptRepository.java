package com.esource.stormmetricservice.ami.repository;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import com.esource.stormmetricservice.ami.model.AmiIngestionAttempt;

@Repository
public interface AmiIngestionAttemptRepository extends MongoRepository<AmiIngestionAttempt, String> {
    List<AmiIngestionAttempt> findByStatusAndNextAttemptTimeBefore(
            AmiIngestionAttempt.IngestionStatus status, ZonedDateTime time);

    Optional<AmiIngestionAttempt> findByStormId(String stormId);

    List<AmiIngestionAttempt> findByStatus(AmiIngestionAttempt.IngestionStatus status);
}
