package com.esource.stormmetricservice.ami.service;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.ami.exception.AmiIngestionException;
import com.esource.stormmetricservice.ami.service.auth.DatabricksConnectionService;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@ConditionalOnProperty(value = "ami.ingestion.enabled", havingValue = "true")
@RequiredArgsConstructor
public class AmiChangeIngestionQueueService {
    private final LinkedBlockingQueue<EndStormEvent> ingestionQueue = new LinkedBlockingQueue<>();
    private final AtomicBoolean isRunning = new AtomicBoolean(true);
    private Thread processingThread;
    private Connection sharedConnection;

    private final AmiMeterChangeIngestionService ingestionService;
    private final DatabricksConnectionService connectionService;

    @PostConstruct
    public void init() {
        try {
            sharedConnection = connectionService.createConnection();
            log.info("Established shared Databricks connection for AMI Change Ingestion Queue");

            processingThread =
                    Thread.ofVirtual().name("ami-change-ingestion-processor").start(this::processQueueItems);
            log.info("AMI Change Ingestion Queue Service initialized");
        } catch (SQLException e) {
            log.error("Failed to establish initial Databricks connection", e);
            throw new AmiIngestionException("Failed to initialize AMI Change Ingestion Queue Service", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        log.info("Shutting down AMI Change Ingestion Queue Service");
        isRunning.set(false);
        processingThread.interrupt();

        if (sharedConnection != null) {
            try {
                sharedConnection.close();
                log.info("Closed shared Databricks connection");
            } catch (SQLException e) {
                log.error("Error closing shared Databricks connection", e);
            }
        }
    }

    public void queueStormEvent(EndStormEvent event) {
        try {
            ingestionQueue.put(event);
            log.info("Queued storm event for ingestion. Queue size: {}", ingestionQueue.size());
        } catch (InterruptedException e) {
            log.error("Failed to queue storm event for ingestion", e);
            Thread.currentThread().interrupt();
        }
    }

    private void processQueueItems() {
        while (isRunning.get()) {
            try {
                EndStormEvent event = ingestionQueue.take();
                log.info("Processing queued storm event. Remaining queue size: {}", ingestionQueue.size());
                processStormEvent(event);
            } catch (InterruptedException e) {
                if (isRunning.get()) {
                    log.error("Queue processing interrupted", e);
                }
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    private void processStormEvent(EndStormEvent event) {
        try {
            ensureValidConnection();
            ingestionService.ingestAmiMeterChanges(event, sharedConnection);
            log.info("Successfully processed storm event ingestion");
        } catch (Exception e) {
            log.error("Error processing storm event ingestion", e);
            refreshConnection();
        }
    }

    private void ensureValidConnection() throws SQLException {
        if (sharedConnection == null || sharedConnection.isClosed()) {
            log.info("Shared connection is null or closed, establishing new connection");
            refreshConnection();
        } else {
            try {
                sharedConnection.createStatement().execute("SELECT 1");
            } catch (SQLException e) {
                log.warn("Connection test failed, refreshing connection", e);
                refreshConnection();
            }
        }
    }

    private void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                log.warn("Error closing connection", e);
            }
        }
    }

    private synchronized void refreshConnection() {
        closeConnection(sharedConnection);
        try {
            sharedConnection = connectionService.createConnection();
            log.info("Successfully refreshed Databricks connection");
        } catch (SQLException e) {
            log.error("Failed to refresh Databricks connection", e);
            throw new AmiIngestionException("Failed to refresh Databricks connection", e);
        }
    }
}
