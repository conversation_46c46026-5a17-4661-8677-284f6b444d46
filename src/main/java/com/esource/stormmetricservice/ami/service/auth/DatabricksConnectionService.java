package com.esource.stormmetricservice.ami.service.auth;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.config.ami.AmiIngestionProperties;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@ConditionalOnProperty(value = "ami.ingestion.enabled", havingValue = "true")
@RequiredArgsConstructor
public class DatabricksConnectionService {

    private final AmiIngestionProperties amiProperties;
    private final AzureAuthService authService;

    public Connection createConnection() throws SQLException {
        var databricksProperties = amiProperties.databricks();
        System.setProperty("com.databricks.jdbc.loggerImpl", "SLF4JLOGGER");

        try {
            Class.forName("com.databricks.client.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            throw new SQLException("Failed to load Databricks JDBC driver", e);
        }

        String accessToken = authService.getAccessToken();
        String jdbcUrl = String.format(
                "jdbc:databricks://%s:%d/%s",
                databricksProperties.host(), databricksProperties.port(), databricksProperties.catalogName());

        Properties connectionProperties = new Properties();
        connectionProperties.put("httpPath", databricksProperties.httpPath());
        connectionProperties.put("SSL", "1");
        connectionProperties.put("UserAgentEntry", "storm-metric-service");
        connectionProperties.put("AuthMech", "11");
        connectionProperties.put("Auth_Flow", "0");
        connectionProperties.put("Auth_AccessToken", accessToken);
        connectionProperties.put("UseNativeQuery", "1");
        connectionProperties.put("use_cached_result", "true");
        connectionProperties.put("ConnCatalog", databricksProperties.catalogName());
        connectionProperties.put("ConnSchema", "apc_meter");
        connectionProperties.put("statement_timeout", "172800");
        connectionProperties.put("EnableArrow", "0");

        log.info(
                "Attempting to connect to Azure Databricks with URL pattern: jdbc:databricks://<host>:<port>/{}",
                databricksProperties.catalogName());

        try {
            return DriverManager.getConnection(jdbcUrl, connectionProperties);
        } catch (SQLException e) {
            log.error("Failed to establish Azure Databricks connection: {}", e.getMessage());
            throw new SQLException("Failed to create Azure Databricks connection", e);
        }
    }
}
