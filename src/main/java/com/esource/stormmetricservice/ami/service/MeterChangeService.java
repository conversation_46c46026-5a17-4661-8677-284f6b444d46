package com.esource.stormmetricservice.ami.service;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.ami.model.AmiMeterChangeDocument;
import com.esource.stormmetricservice.ami.model.MeterChangesResponseDto;
import com.esource.stormmetricservice.ami.repository.AmiChangeRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class MeterChangeService {
    private final AmiChangeRepository amiMeterRepository;

    public MeterChangesResponseDto getMeterChangesByTimeRange(ZonedDateTime start, ZonedDateTime end) {
        List<AmiMeterChangeDocument> meterChanges = amiMeterRepository.findMeterChangesByTimeRange(start, end);
        return MeterChangesResponseDto.builder()
                .meterSessions(processAmiMeterSessions(meterChanges))
                .build();
    }

    private List<MeterChangesResponseDto.AmiMeterSession> processAmiMeterSessions(
            List<AmiMeterChangeDocument> meterChanges) {
        return meterChanges.stream().map(this::convertToAmiMeterSession).toList();
    }

    private MeterChangesResponseDto.AmiMeterSession convertToAmiMeterSession(AmiMeterChangeDocument change) {
        return MeterChangesResponseDto.AmiMeterSession.builder()
                .timestamp(change.getTimestamp())
                .meters(change.getMetersChanged().stream()
                        .collect(Collectors.toMap(
                                AmiMeterChangeDocument.MeterChange::getMeterId,
                                AmiMeterChangeDocument.MeterChange::getStatus)))
                .build();
    }
}
