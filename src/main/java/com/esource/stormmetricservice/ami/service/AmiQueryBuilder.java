package com.esource.stormmetricservice.ami.service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import com.esource.stormmetricservice.ami.exception.AmiIngestionException;

@Component
public class AmiQueryBuilder {
    private final String meterChangesQueryTemplate;

    public AmiQueryBuilder() {
        try (var inputStream = new ClassPathResource("sql/ami/meter-ingest.sql").getInputStream()) {
            meterChangesQueryTemplate = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new AmiIngestionException("Failed to load meter changes query template", e);
        }
    }

    public String buildMeterChangesQuery(ZonedDateTime startTime, ZonedDateTime endTime) {
        String startTimeStr = formatDateTime(startTime);
        String endTimeStr = formatDateTime(endTime);

        return meterChangesQueryTemplate
                .replace(":startTime", "'" + startTimeStr + "'")
                .replace(":endTime", "'" + endTimeStr + "'");
    }

    private String formatDateTime(ZonedDateTime dateTime) {
        return dateTime.withZoneSameInstant(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
