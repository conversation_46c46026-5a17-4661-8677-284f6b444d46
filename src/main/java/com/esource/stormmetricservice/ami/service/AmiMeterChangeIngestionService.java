package com.esource.stormmetricservice.ami.service;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import com.esource.stormmetricservice.ami.exception.AmiIngestionException;
import com.esource.stormmetricservice.ami.model.AmiIngestionAttempt;
import com.esource.stormmetricservice.ami.model.AmiIngestionAttempt.IngestionStatus;
import com.esource.stormmetricservice.ami.model.AmiMeterChangeDocument;
import com.esource.stormmetricservice.ami.model.AmiMeterChangeDocument.MeterChange;
import com.esource.stormmetricservice.ami.repository.AmiChangeRepository;
import com.esource.stormmetricservice.ami.repository.AmiIngestionAttemptRepository;
import com.esource.stormmetricservice.config.ami.AmiRetryProperties;
import com.esource.stormmetricservice.metrics.model.StormOverview;
import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@ConditionalOnProperty(value = "ami.ingestion.enabled", havingValue = "true")
@RequiredArgsConstructor
public class AmiMeterChangeIngestionService {
    private static final int BATCH_SIZE = 1000;

    private final AmiChangeRepository amiChangeRepository;
    private final AmiIngestionAttemptRepository attemptRepository;
    private final AmiQueryBuilder queryBuilder;
    private final ObjectMapper objectMapper;
    private final AmiRetryProperties retryProperties;

    public void ingestAmiMeterChanges(EndStormEvent endStormEvent, Connection connection) {
        AmiIngestionAttempt attempt = getOrCreateAttempt(endStormEvent);

        try {
            attempt.setStatus(IngestionStatus.IN_PROGRESS);
            attempt.setLastAttemptTime(ZonedDateTime.now());
            attemptRepository.save(attempt);

            List<AmiMeterChangeDocument> results = performIngestion(endStormEvent, connection);
            processIngestionResults(attempt, results);

        } catch (Exception e) {
            handleIngestionFailure(attempt, e);
        }
    }

    private AmiIngestionAttempt getOrCreateAttempt(EndStormEvent event) {
        return attemptRepository
                .findByStormId(event.getStormData().getId())
                .orElse(AmiIngestionAttempt.builderWithStormEvent(event)
                        .attemptCount(0)
                        .status(IngestionStatus.PENDING)
                        .build());
    }

    private List<AmiMeterChangeDocument> performIngestion(EndStormEvent event, Connection connection)
            throws SQLException, IOException {
        StormOverview stormData = event.getStormData();
        cleanupExistingRecords(stormData);

        String sqlQuery = queryBuilder.buildMeterChangesQuery(stormData.getStartDate(), stormData.getEndDate());

        return executeIngestionQuery(sqlQuery, connection);
    }

    private void cleanupExistingRecords(StormOverview stormData) {
        try {
            ZonedDateTime startTime = stormData.getStartDate();
            ZonedDateTime endTime = stormData.getEndDate();

            List<AmiMeterChangeDocument> existingRecords =
                    amiChangeRepository.findMeterChangesByTimeRange(startTime, endTime);

            if (!existingRecords.isEmpty()) {
                log.info(
                        "Deleting {} existing records in time range {} to {}",
                        existingRecords.size(),
                        startTime,
                        endTime);
                amiChangeRepository.deleteByTimeRange(startTime, endTime);
            }
        } catch (Exception e) {
            log.error("Error cleaning up existing records", e);
            throw new AmiIngestionException("Failed to clean up existing records", e);
        }
    }

    private List<AmiMeterChangeDocument> executeIngestionQuery(String sqlQuery, Connection connection)
            throws SQLException, IOException {
        List<AmiMeterChangeDocument> allDocuments = new ArrayList<>();
        List<AmiMeterChangeDocument> batchDocuments = new ArrayList<>();

        try (Statement statement = connection.createStatement()) {
            log.info("Executing query:\n{}", sqlQuery);
            ResultSet resultSet = statement.executeQuery(sqlQuery);

            int totalRows = 0;
            int batchCount = 0;
            long startTimeMillis = System.currentTimeMillis();

            while (resultSet.next()) {
                AmiMeterChangeDocument document = processResultSetRow(resultSet);
                batchDocuments.add(document);
                totalRows++;

                if (batchDocuments.size() >= BATCH_SIZE) {
                    processBatch(batchDocuments, totalRows, batchCount, startTimeMillis);
                    allDocuments.addAll(batchDocuments);
                    batchDocuments.clear();
                    batchCount++;
                }
            }

            if (!batchDocuments.isEmpty()) {
                processBatch(batchDocuments, totalRows, batchCount, startTimeMillis);
                allDocuments.addAll(batchDocuments);
            }

            logIngestionCompletion(totalRows, batchCount, startTimeMillis);
        }

        return allDocuments;
    }

    private AmiMeterChangeDocument processResultSetRow(ResultSet resultSet) throws SQLException, IOException {
        ZonedDateTime timestamp =
                resultSet.getTimestamp("timestamp").toInstant().atZone(ZoneId.of("UTC"));

        String metersChangedJson = resultSet.getString("metersChanged");
        int changeCount = resultSet.getInt("number_of_changes");

        List<MeterChange> metersChanged = parseMetersChanged(metersChangedJson);
        validateChangeCount(changeCount, metersChanged, timestamp);

        return new AmiMeterChangeDocument(timestamp, metersChanged);
    }

    private void processBatch(List<AmiMeterChangeDocument> batch, int totalRows, int batchCount, long startTimeMillis) {
        amiChangeRepository.saveAll(batch);
        logBatchProgress(batch, totalRows, batchCount, startTimeMillis);
    }

    private void processIngestionResults(AmiIngestionAttempt attempt, List<AmiMeterChangeDocument> results) {
        double percentDataPresent = calculatePercentOfMetersChanged(results);
        attempt.setPercentDataPresent(percentDataPresent);

        if (percentDataPresent >= retryProperties.minimumSuccessRate()) {
            attempt.setStatus(IngestionStatus.SUCCESSFUL);
            attempt.setNextAttemptTime(null);
        } else {
            attempt.setStatus(IngestionStatus.NEEDS_RETRY);
            attempt.setNextAttemptTime(ZonedDateTime.now().plusHours(retryProperties.retryDelayHours()));
            attempt.setAttemptCount(attempt.getAttemptCount() + 1);

            if (!attempt.shouldRetry()) {
                attempt.setStatus(IngestionStatus.FAILED);
                attempt.setNextAttemptTime(null);
            }
        }

        attemptRepository.save(attempt);

        if (attempt.getStatus() == IngestionStatus.NEEDS_RETRY) {
            log.info(
                    "Ingestion partially successful ({}%). Scheduled retry for {}",
                    percentDataPresent * 100, attempt.getNextAttemptTime());
        }
    }

    private void handleIngestionFailure(AmiIngestionAttempt attempt, Exception e) {
        log.error("Error during ingestion attempt", e);
        attempt.setStatus(IngestionStatus.FAILED);
        attempt.setNextAttemptTime(ZonedDateTime.now().plusHours(retryProperties.retryDelayHours()));
        attempt.setAttemptCount(attempt.getAttemptCount() + 1);
        attemptRepository.save(attempt);

        throw new AmiIngestionException("Failed to ingest AMI meter changes", e);
    }

    private double calculatePercentOfMetersChanged(List<AmiMeterChangeDocument> results) {
        if (results.isEmpty()) return 0.0;

        long intervalsWithChanges = results.stream()
                .filter(doc -> !doc.getMetersChanged().isEmpty())
                .count();

        return (double) intervalsWithChanges / results.size();
    }

    private void validateChangeCount(int expectedCount, List<MeterChange> actualChanges, ZonedDateTime timestamp) {
        if (expectedCount != actualChanges.size()) {
            log.warn(
                    "Mismatch in change counts - SQL: {}, Parsed: {} at timestamp {}",
                    expectedCount,
                    actualChanges.size(),
                    timestamp);
        }
    }

    private List<MeterChange> parseMetersChanged(String metersChangedJson) throws IOException {
        if (metersChangedJson == null || metersChangedJson.isEmpty()) {
            return Collections.emptyList();
        }

        List<MeterChange> changes =
                objectMapper.readValue(metersChangedJson, new TypeReference<List<MeterChange>>() {});

        logSampleChanges(changes);
        return changes;
    }

    private void logSampleChanges(List<MeterChange> changes) {
        if (!changes.isEmpty() && log.isDebugEnabled()) {
            log.debug(
                    "Sample meter changes (first {} of {}): {}",
                    Math.min(3, changes.size()),
                    changes.size(),
                    changes.subList(0, Math.min(3, changes.size())));
        }
    }

    private void logBatchProgress(
            List<AmiMeterChangeDocument> batch, int totalRows, int batchCount, long startTimeMillis) {
        long elapsedSeconds = (System.currentTimeMillis() - startTimeMillis) / 1000;
        double rowsPerSecond = totalRows / (double) Math.max(1, elapsedSeconds);

        log.info(
                "Progress: processed {} total records ({} batches) at {:.2f} records/second",
                totalRows,
                batchCount,
                rowsPerSecond);

        if (!batch.isEmpty()) {
            AmiMeterChangeDocument lastDoc = batch.get(batch.size() - 1);
            log.info("Last timestamp processed: {}", lastDoc.getTimestamp());

            if (!lastDoc.getMetersChanged().isEmpty()) {
                log.info(
                        "Sample batch size: {} meter changes for timestamp {}",
                        lastDoc.getMetersChanged().size(),
                        lastDoc.getTimestamp());
            }
        }
    }

    private void logIngestionCompletion(int totalRows, int batchCount, long startTimeMillis) {
        long totalTimeSeconds = (System.currentTimeMillis() - startTimeMillis) / 1000;
        log.info("Ingestion complete: {} total records in {} batches", totalRows, batchCount);
        log.info(
                "Total processing time: {} seconds ({} records/second)",
                totalTimeSeconds,
                String.format("%.2f", totalRows / (double) Math.max(1, totalTimeSeconds)));
    }
}
