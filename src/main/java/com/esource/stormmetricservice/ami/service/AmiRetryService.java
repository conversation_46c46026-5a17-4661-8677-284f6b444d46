package com.esource.stormmetricservice.ami.service;

import java.time.ZonedDateTime;
import java.util.List;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.esource.stormmetricservice.ami.model.AmiIngestionAttempt;
import com.esource.stormmetricservice.ami.model.AmiIngestionAttempt.IngestionStatus;
import com.esource.stormmetricservice.ami.repository.AmiIngestionAttemptRepository;
import com.esource.stormmetricservice.config.ami.AmiRetryProperties;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@ConditionalOnProperty(value = "ami.ingestion.enabled", havingValue = "true", matchIfMissing = false)
@RequiredArgsConstructor
public class AmiRetryService {
    private final AmiIngestionAttemptRepository attemptRepository;
    private final AmiChangeIngestionQueueService queueService;
    private final AmiRetryProperties retryProperties;
    private final TaskScheduler taskScheduler;

    @PostConstruct
    public void initializeRetries() {
        List<AmiIngestionAttempt> pendingRetries = attemptRepository.findByStatus(IngestionStatus.NEEDS_RETRY);

        for (AmiIngestionAttempt attempt : pendingRetries) {
            if (attempt.getNextAttemptTime().isBefore(ZonedDateTime.now())) {
                attempt.setNextAttemptTime(ZonedDateTime.now().plusSeconds(20));
                attemptRepository.save(attempt);
            }
            scheduleNextRetry(attempt);
        }
        log.info("Scheduled {} pending retry attempts", pendingRetries.size());
    }

    private void scheduleNextRetry(AmiIngestionAttempt attempt) {
        if (attempt.getNextAttemptTime() != null) {
            taskScheduler.schedule(
                    () -> retryAttempt(attempt), attempt.getNextAttemptTime().toInstant());

            log.debug("Scheduled retry for attempt {} at {}", attempt.getId(), attempt.getNextAttemptTime());
        }
    }

    @Transactional
    public void retryAttempt(AmiIngestionAttempt attempt) {

        if (attempt.getStatus() != IngestionStatus.NEEDS_RETRY) {
            log.debug("Skipping retry for attempt {} as status is {}", attempt.getStormId(), attempt.getStatus());
            return;
        }

        if (!attempt.shouldRetry()) {
            markAttemptAsFailed(attempt);
            return;
        }

        try {
            processRetryAttempt(attempt);
        } catch (Exception e) {
            log.error(
                    "Failed to process retry for storm {}",
                    attempt.getEvent().getStormData().getId(),
                    e);
            handleRetryError(attempt);
        }
    }

    private void processRetryAttempt(AmiIngestionAttempt attempt) {
        attempt.setAttemptCount(attempt.getAttemptCount() + 1);
        attempt.setLastAttemptTime(ZonedDateTime.now());
        attemptRepository.save(attempt);

        log.info(
                "Queued retry attempt {} for storm {}",
                attempt.getAttemptCount(),
                attempt.getEvent().getStormData().getId());

        queueService.queueStormEvent(attempt.getEvent());
    }

    private void markAttemptAsFailed(AmiIngestionAttempt attempt) {
        attempt.setStatus(IngestionStatus.FAILED);
        attempt.setNextAttemptTime(null);
        attemptRepository.save(attempt);

        log.warn(
                "Storm {} marked as failed after {} attempts. Storm start: {}, Last attempt: {}",
                attempt.getEvent().getStormData().getId(),
                attempt.getAttemptCount(),
                attempt.getEvent().getStormData().getStartDate(),
                attempt.getLastAttemptTime());
    }

    private void handleRetryError(AmiIngestionAttempt attempt) {
        if (attempt.shouldRetry()) {
            attempt.setNextAttemptTime(ZonedDateTime.now().plusHours(retryProperties.retryDelayHours()));
            attemptRepository.save(attempt);
            scheduleNextRetry(attempt);
        } else {
            markAttemptAsFailed(attempt);
        }
    }
}
