package com.esource.stormmetricservice.ami.service.auth;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import com.esource.stormmetricservice.config.ami.AmiIngestionProperties;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@ConditionalOnProperty(value = "ami.ingestion.enabled", havingValue = "true")
@RequiredArgsConstructor
public class AzureAuthService {
    private static final String TOKEN_URL_TEMPLATE = "https://login.microsoftonline.com/%s/oauth2/v2.0/token";

    private final AmiIngestionProperties amiProperties;
    private final RestTemplate restTemplate;

    public String getAccessToken() {
        var azureProperties = amiProperties.azure();
        String tokenUrl = String.format(TOKEN_URL_TEMPLATE, azureProperties.tenantId());

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("client_id", azureProperties.clientId());
        body.add("client_secret", azureProperties.clientSecret());
        body.add("grant_type", "client_credentials");
        body.add("scope", azureProperties.scope());

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

        try {
            ResponseEntity<TokenResponse> response = restTemplate.postForEntity(tokenUrl, request, TokenResponse.class);

            if (response.getBody() != null) {
                return response.getBody().accessToken();
            }
            throw new DatabricksAuthenticationException("Failed to retrieve access token: Response body is null");
        } catch (Exception e) {
            log.error("Error retrieving Azure access token", e);
            throw new DatabricksAuthenticationException("Failed to retrieve Azure access token", e);
        }
    }

    private record TokenResponse(String access_token) {
        public String accessToken() {
            return access_token;
        }
    }
}
