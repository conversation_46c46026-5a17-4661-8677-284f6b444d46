package com.esource.stormmetricservice.ami.controller;

import java.time.ZonedDateTime;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.esource.stormmetricservice.ami.model.MeterChangesResponseDto;
import com.esource.stormmetricservice.ami.service.MeterChangeService;

import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

@Validated
@RestController
@RequestMapping("/meters")
@RequiredArgsConstructor
public class MeterChangeController {
    private final MeterChangeService meterChangeService;

    @GetMapping("/ami-replay")
    public MeterChangesResponseDto getAmiMeters(
            @Parameter(description = "Start time (ISO DATE TIME format)")
                    @RequestParam
                    @NotNull @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                    ZonedDateTime stormStart,
            @Parameter(description = "End time (ISO DATE TIME format)")
                    @RequestParam
                    @NotNull @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
                    ZonedDateTime stormEnd) {

        return meterChangeService.getMeterChangesByTimeRange(stormStart, stormEnd);
    }
}
