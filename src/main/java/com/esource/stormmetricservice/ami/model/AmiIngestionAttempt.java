package com.esource.stormmetricservice.ami.model;

import java.time.ZonedDateTime;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.esource.stormmetricservice.stormevents.model.messaging.EndStormEvent;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Document(collection = "ami_ingest_log")
public class AmiIngestionAttempt {
    @Id
    private String id;

    private String stormId;
    private EndStormEvent event;
    private int attemptCount;
    private ZonedDateTime lastAttemptTime;
    private ZonedDateTime nextAttemptTime;
    private IngestionStatus status;
    private double percentDataPresent;

    public enum IngestionStatus {
        PENDING,
        IN_PROGRESS,
        NEEDS_RETRY,
        SUCCESSFUL,
        FAILED
    }

    public boolean shouldRetry() {
        return status == IngestionStatus.NEEDS_RETRY
                && attemptCount < 30
                && nextAttemptTime != null
                && nextAttemptTime.isBefore(ZonedDateTime.now().plusDays(30));
    }

    public void setEvent(EndStormEvent endEvent) {
        this.event = endEvent;
        if (endEvent != null && endEvent.getStormData() != null) {
            this.stormId = endEvent.getStormData().getId();
        }
    }

    public static AmiIngestionAttemptBuilder builderWithStormEvent(EndStormEvent event) {
        return builder().event(event).stormId(event.getStormData().getId());
    }
}
