package com.esource.stormmetricservice.ami.model;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MeterChangesResponseDto {

    private List<AmiMeterSession> meterSessions;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AmiMeterSession {
        private ZonedDateTime timestamp;
        private Map<String, Integer> meters;
    }
}
