package com.esource.stormmetricservice.ami.model;

import java.time.ZonedDateTime;
import java.util.List;

import org.springframework.data.mongodb.core.mapping.TimeSeries;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TimeSeries(collection = "delta_ami_meter_changes", timeField = "timestamp")
public class AmiMeterChangeDocument {
    private ZonedDateTime timestamp;
    private List<MeterChange> metersChanged;

    @Data
    public static class MeterChange {
        private String meterId;
        private int status;
    }
}
