# Storm Metric Service

## Data Organization

### Contents

1. [Data Collection](#data-collection)
2. [Metrics](#data-collection)
	1. [Continuous Aggregate Metrics](#continuous-aggregate-metrics-overview-collection)
	2. [Storm-Level Metrics](#storm-level-metrics-metric_document-collection)
	3. [Summary Metrics](#summary-retrospective-metrics-storm_retrospective)
3. [Events](#events)
	1. [Aggregate-Overview Event](#aggregate-overview-event)
	2. [Storm-End Event](#storm-end-event)

### Data Collection

**Current Metric Organization**

![Metric Organization Diagram](https://mermaid.ink/img/pako:eNpdkD1uwzAMha8icHIK-wIeAiT92YoO7WYFhmoxsgBLMijKQRvn7lVcpwi6SeTHx8d3hi5ohBqOQzh1vSIWH3vpY_o0pMZeKGMIjWKUXlvCjm3wC_GWWBl8QdSiqrZi_gPbMCFNFk8tTuh5FrumuJU2B-nR6zv9yIFcNWR0-L9haT37VX_5tXn4JrtvirvhyiGT7eLmcMXnh5icU2S_sz3rOczisSkoIyGO1xUTrlagBIfklNU5g7P0QkjgHh1KqPNzsKZnCdJfMqgSh_cv30HNlLCENOp875NV-RQH9VENMVdR22zr9TfVJdwSKCTTr8TlB7aIhrY?type=png)

Storm data is primarily organized at two levels of granularity: continuous, and storm-level. Storm-level metric data is further organized into retrospectives. Overview metric data is gathered continuously, whereas storm-level metrics are only gathered when a storm ends. 

#### Continuous Aggregate Metrics (overview Collection)

![Continuous Aggregate Metrics Diagram](https://mermaid.ink/img/pako:eNp1j8EKwjAQRH8l7Emh_YEehIp4Ew96cz0syZoG2kS2mxYR_91gBU_ehseDmXmCTY6hgVufZtuRqDlvMbaXVZpYpsDz-ooRI1kNE7feC3tSdses5Hk0db0xLUbhMWWxP5Aj_XNnJu1Y9sxuQVDBwDJQcGXHE6MxCMUYGKEpsQ--UwSMryJS1nR6RAuNSuYK8t2Vil0gLzRAc6N-LJRd0CSH5dnnYAWSsu--xusNtQdVLQ?type=png)

Because the most granular (or frequently updated) data across ETR-enabled clients is *outage* data, the `aggregate-overview` event is configured to be triggered by incoming outage data. This event spawns aggregators which organize the latest outage, resource, and weather data into a single overview metric. 

##### Overview Entity

[![Overview Entity](https://mermaid.ink/img/pako:eNqFk81qwzAMx1_F-Lr0BXIYjBZG2UqhHeySi2ariSGxO1tuGaV99rmJkyUh6XxwHOmHPv62LlwYiTzlogTnVgpyC1WmWVi1hW1PaE8Kz-zSWO_raU9W6Zwp2bO9GwHlCgg_VIWMwuYIqmOPaEMFCJgMW-O7ZnoqYU31k27geGsSJ2zrCXK8MVN_3Qy1Q2e8FYGz8TRHfiJQgfbGzs1hurI62aAmpYmRISjXWiiJmtyU8-VwQEEol96RqdCOIa_Vt8cuxNJ4TZPITKBRnW3b05Xu_pFirQnzuxQjzUZJomKzNxT9ewJSRnfCxv-HMSMzCC2N_yqRnZWWr6H5t6oYReje6WLxPHxDKRNGE6g26cBZ0829pqyAOaST9BHUStJjWlPP3Xb3VxZPeLjLCpQMc1g3nfFAVpjxNBxLlReU8UxfAwiezP5HC56S9ZhwfwyDhHFueXqA0gUrSkXGbuJk3z8Jt8bnRSSuv1DUTGA?type=png)](https://mermaid.live/edit#pako:eNqFk81qwzAMx1_F-Lr0BXIYjBZG2UqhHeySi2ariSGxO1tuGaV99rmJkyUh6XxwHOmHPv62LlwYiTzlogTnVgpyC1WmWVi1hW1PaE8Kz-zSWO_raU9W6Zwp2bO9GwHlCgg_VIWMwuYIqmOPaEMFCJgMW-O7ZnoqYU31k27geGsSJ2zrCXK8MVN_3Qy1Q2e8FYGz8TRHfiJQgfbGzs1hurI62aAmpYmRISjXWiiJmtyU8-VwQEEol96RqdCOIa_Vt8cuxNJ4TZPITKBRnW3b05Xu_pFirQnzuxQjzUZJomKzNxT9ewJSRnfCxv-HMSMzCC2N_yqRnZWWr6H5t6oYReje6WLxPHxDKRNGE6g26cBZ0829pqyAOaST9BHUStJjWlPP3Xb3VxZPeLjLCpQMc1g3nfFAVpjxNBxLlReU8UxfAwiezP5HC56S9ZhwfwyDhHFueXqA0gUrSkXGbuJk3z8Jt8bnRSSuv1DUTGA)

#### Storm-Level Metrics (metric_document Collection)

Storm-level metrics are gathered when a storm moves from post-restoration to archive. This launches a `storm-end-event`, which triggers resource, outage, and weather collectors to organize data into storm metrics. Importantly, ETR metrics, which are only available during an active storm, are also collected here, unlike in the continuous aggregate metrics. 

[![Storm-level metrics diagram](https://mermaid.ink/img/pako:eNpVUbFuAjEM_RUrMwygTje0UgtSBbQDsFR3qEoTc4maxMiXUCHg3-vrUVX1FL_3bL_YZ2XIoqrUPtCXcZozrNZNahJIdOWjZX1wMD9iyt0A9mE9o8meEmwf_9DFpH6mwuE0367BODSfyDsYj-9hMT1vMnGESEe0sGeKwNgJJFkm0GycF-bhOnTDZH89LKZ9h8sbdhdYTuot-7ZFhr42vosOsPc2jFlO6ycKQawN_DgIGSBiZm-6m-buT1Ni1HwSJ5mpO_Q_OuLu39hXusCqnhEkys6nVlg1UhE5am9la-de3ajsMGKjKnkG37rcqCZdRahLps0pGVVlLjhS5WB1xpnXstaoqr0OnaBovbh9Ge7wc46RYiqtuymu327VjL4?type=png)](https://mermaid.live/edit#pako:eNpVUbFuAjEM_RUrMwygTje0UgtSBbQDsFR3qEoTc4maxMiXUCHg3-vrUVX1FL_3bL_YZ2XIoqrUPtCXcZozrNZNahJIdOWjZX1wMD9iyt0A9mE9o8meEmwf_9DFpH6mwuE0367BODSfyDsYj-9hMT1vMnGESEe0sGeKwNgJJFkm0GycF-bhOnTDZH89LKZ9h8sbdhdYTuot-7ZFhr42vosOsPc2jFlO6ycKQawN_DgIGSBiZm-6m-buT1Ni1HwSJ5mpO_Q_OuLu39hXusCqnhEkys6nVlg1UhE5am9la-de3ajsMGKjKnkG37rcqCZdRahLps0pGVVlLjhS5WB1xpnXstaoqr0OnaBovbh9Ge7wc46RYiqtuymu327VjL4)

Storm-level metrics are usually defined as metrics between the start and end of a storm, plus 6 hours (where applicable). In the future, it is be possible to deprecate several of the storm-level metric "collectors" and replace them with overview "aggregators" to maintain single sources of truth and simplify maintenance. 

##### Metric Entity

[![Metric Entity Diagram](https://mermaid.ink/img/pako:eNqlVsGO2jAQ_RXL18IPRFUvywWpiGqh6oWL154GdxMb2RNWKxa-vRNiaOI42ew2BwKe997M2I9JTlxaBTzjshDeL7TInSh3htF1XWEbtK5cATot2akJ1NeX79rjpVm_sPJ6963w-ukPSFwq5mv-UrVCG8KaPHC2rwfoxAi9PoI7anhpuAuBop3XSlHQGmx1CYxKxJ8HRT9DhvPO9Kq_6536VWg1qO1ROKx_DSLAqCgeVI0oIVlPaiM7kkgflLg8JOkPVVkVAvUR1hWKHIbOJcY9grROXZi73n1Se1xxus4PB0pL1NYMaf1DJPXaG-nBe8It06f7CN5WTg4WfYv_fxqykRhrqQWZskfp80n500FOmq31pUHIwTF5l3ioKHcJrpHyo9ilkVqBwXFU0w2ojyjfOFGGpMk-0a6Q01ptcONFxP6bVMjCVk8FsD0Zym9t67z7XLzPtAG_fjwtWhTFL-uewbW7XonDpWHOWNiB2nhNFv-emT9xDG6yM9w7fmg_V-bzb7fJmDFpDQptfBrWHegZ24sAHBiNNWfg3xan6vFG0b0xVzN6zopZ0cSqOZEpEoxo-ARSdIptXsMM8K9v8_nA7vRx49G45z6i218y3u2GzzjZqBRa0TvI1Yk7jnugxyfP6Guh8z3u-M6cCSgqtJtXI3mGroIZr64P_vDOwrPfovC0SiVSilV4q6lvM-5sle8D4vwXrMr5hQ?type=png)](https://mermaid.live/edit#pako:eNqlVsGO2jAQ_RXL18IPRFUvywWpiGqh6oWL154GdxMb2RNWKxa-vRNiaOI42ew2BwKe997M2I9JTlxaBTzjshDeL7TInSh3htF1XWEbtK5cATot2akJ1NeX79rjpVm_sPJ6963w-ukPSFwq5mv-UrVCG8KaPHC2rwfoxAi9PoI7anhpuAuBop3XSlHQGmx1CYxKxJ8HRT9DhvPO9Kq_6536VWg1qO1ROKx_DSLAqCgeVI0oIVlPaiM7kkgflLg8JOkPVVkVAvUR1hWKHIbOJcY9grROXZi73n1Se1xxus4PB0pL1NYMaf1DJPXaG-nBe8It06f7CN5WTg4WfYv_fxqykRhrqQWZskfp80n500FOmq31pUHIwTF5l3ioKHcJrpHyo9ilkVqBwXFU0w2ojyjfOFGGpMk-0a6Q01ptcONFxP6bVMjCVk8FsD0Zym9t67z7XLzPtAG_fjwtWhTFL-uewbW7XonDpWHOWNiB2nhNFv-emT9xDG6yM9w7fmg_V-bzb7fJmDFpDQptfBrWHegZ24sAHBiNNWfg3xan6vFG0b0xVzN6zopZ0cSqOZEpEoxo-ARSdIptXsMM8K9v8_nA7vRx49G45z6i218y3u2GzzjZqBRa0TvI1Yk7jnugxyfP6Guh8z3u-M6cCSgqtJtXI3mGroIZr64P_vDOwrPfovC0SiVSilV4q6lvM-5sle8D4vwXrMr5hQ)

#### Summary Retrospective Metrics (storm_retrospective)

At the highest/most processed level, data is stored in summary retrospective metrics. Summary retrospective metrics, like storm-level metrics, are also collected at the end of a storm, but they instead contain summary metrics intended to allow users to compare storm impact per storm. 

##### Retrospective Entity

[![Retrospective Entity](https://mermaid.ink/img/pako:eNqFUk1rwzAM_SvG16V_IIfBaMcorAyWwS65aLaaGGI72HJHKe1vn1Kn3yv1xbb0nvT8rI1UXqMspeogxpmBJoCtneC1j4iKfLCfSMHHHhWZFYpNzg_rqaJgXCOMvo3FgTm_THDkY4VhZfA35w-3M9QC-l0uUeTur05XyVoI610mnQKZtq3djeRjm0dq372CbgaEX8Yi14dAw-0uAp2-yo9VHVi8r-ek-ULRzKefDgV5gm6abOpgsHjulNHoKD5EThM7YjHEl-WSvwf1HSPnjrDBsMv8b-P0GzMrgkOPo-Z_Pnwyeb4ytRQtxMf4s0eXQnlHYFyUhWTBFozmsdt7UUtqkc2TJR8707RUy9ptGQiJfLV2SpYUEhYy9Zq9H8dUlkvoIkdRG-62GAd52AoZfGraEbH9A6zT9-M?type=png)](https://mermaid.live/edit#pako:eNqFUk1rwzAM_SvG16V_IIfBaMcorAyWwS65aLaaGGI72HJHKe1vn1Kn3yv1xbb0nvT8rI1UXqMspeogxpmBJoCtneC1j4iKfLCfSMHHHhWZFYpNzg_rqaJgXCOMvo3FgTm_THDkY4VhZfA35w-3M9QC-l0uUeTur05XyVoI610mnQKZtq3djeRjm0dq372CbgaEX8Yi14dAw-0uAp2-yo9VHVi8r-ek-ULRzKefDgV5gm6abOpgsHjulNHoKD5EThM7YjHEl-WSvwf1HSPnjrDBsMv8b-P0GzMrgkOPo-Z_Pnwyeb4ytRQtxMf4s0eXQnlHYFyUhWTBFozmsdt7UUtqkc2TJR8707RUy9ptGQiJfLV2SpYUEhYy9Zq9H8dUlkvoIkdRG-62GAd52AoZfGraEbH9A6zT9-M)

____
### Events

As mentioned, there are two events which drive storm data collection:

1. aggregate overview
2. storm-end event

#### Aggregate-Overview Event

The `aggregate-overview` event is triggered by incoming outage data. This event initiates the aggregation process for various metrics such as outages, resources, and weather data, organizing them into a single overview metric.

- **Trigger:** Incoming outage data
- **Purpose:** Aggregate latest outage, resource, and weather data
- **Result:** Generates an overview metric

#### Storm-End Event

The `storm-end` event is triggered when a storm transitions from post-restoration to archive. This event triggers the collection of storm-level metrics, including ETR metrics, and organizes them into storm metrics. Additionally, this event gathers summary retrospective metrics that provide a comprehensive view of the storm's impact.

- **Trigger:** Storm moves from post-restoration to archive
- **Purpose:** Collect resource, outage, weather, and ETR metrics; gather summary retrospective metrics
- **Result:** Generates storm metrics and summary retrospective metrics

##### Backpopulation

To backpopulate all storm data (except resources), see the [storm event documentation](https://bitbucket.org/trovedata/storm-event-backpopulators/src/main/)

____
